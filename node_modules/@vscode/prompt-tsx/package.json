{"name": "@vscode/prompt-tsx", "version": "0.4.0-alpha.4", "description": "Declare LLM prompts with TSX", "main": "./dist/base/index.js", "types": "./dist/base/index.d.ts", "scripts": {"fmt": "prettier . --write", "prepack": "npm run compile", "compile": "tsx ./build/build-tracer.ts && tsc -p tsconfig.json && tsx ./build/postcompile.ts", "watch": "concurrently \"npm run -s watch:base\" \"npm run -s watch:tracer\"", "watch:tracer": "tsx ./build/build-tracer.ts --watch", "watch:base": "tsc --watch --sourceMap --preserveWatchOutput", "test": "vscode-test", "test:unit": "cross-env IS_OUTSIDE_VSCODE=1 mocha --import=tsx -u tdd \"src/base/test/**/*.test.{ts,tsx}\"", "test:bench": "tsx ./src/base/test/renderer.bench.tsx", "prettier": "prettier --list-different --write --cache .", "prepare": "tsx ./build/postinstall.ts"}, "keywords": [], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-prompt-tsx/issues"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-prompt-tsx.git"}, "homepage": "https://github.com/microsoft/vscode-prompt-tsx#readme", "devDependencies": {"@microsoft/tiktokenizer": "^1.0.6", "@types/node": "^20.11.30", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.1", "concurrently": "^9.0.1", "cross-env": "^7.0.3", "esbuild": "^0.24.0", "mocha": "^10.2.0", "preact": "^10.24.2", "prettier": "^2.8.8", "tinybench": "^3.1.1", "tsx": "^4.19.1", "typescript": "^5.6.2"}}