{"name": "examples", "lockfileVersion": 3, "requires": true, "packages": {"": {"devDependencies": {"@types/vscode": "^1.95.0", "@vscode/prompt-tsx": "file:.."}}, "..": {"version": "0.3.0-alpha.13", "dev": true, "license": "MIT", "devDependencies": {"@microsoft/tiktokenizer": "^1.0.6", "@types/node": "^20.11.30", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.1", "concurrently": "^9.0.1", "cross-env": "^7.0.3", "esbuild": "^0.24.0", "mocha": "^10.2.0", "preact": "^10.24.2", "prettier": "^2.8.8", "tsx": "^4.19.1", "typescript": "^5.6.2"}}, "node_modules/@types/vscode": {"version": "1.95.0", "resolved": "https://registry.npmjs.org/@types/vscode/-/vscode-1.95.0.tgz", "integrity": "sha512-0LBD8TEiNbet3NvWsmn59zLzOFu/txSlGxnv5yAFHCrhG9WvAnR3IvfHzMOs2aeWqgvNjq9pO99IUw8d3n+unw==", "dev": true}, "node_modules/@vscode/prompt-tsx": {"resolved": "..", "link": true}}}