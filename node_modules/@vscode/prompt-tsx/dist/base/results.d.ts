import type { Location, ThemeIcon, Uri } from 'vscode';
import * as J<PERSON><PERSON> from './jsonTypes';
/**
 * Arbitrary metadata which can be retrieved after the prompt is rendered.
 */
export declare abstract class PromptMetadata {
    readonly _marker: undefined;
    toString(): string;
}
export declare enum ChatResponseReferencePartStatusKind {
    Complete = 1,
    Partial = 2,
    Omitted = 3
}
/**
 * A reference used for creating the prompt.
 */
export declare class PromptReference {
    readonly anchor: Uri | Location | {
        variableName: string;
        value?: Uri | Location;
    };
    readonly iconPath?: (Uri | ThemeIcon | {
        light: Uri;
        dark: Uri;
    }) | undefined;
    readonly options?: {
        status?: {
            description: string;
            kind: ChatResponseReferencePartStatusKind;
        };
        isFromTool?: boolean;
    } | undefined;
    static fromJSON(json: JSON.PromptReferenceJSON): PromptReference;
    constructor(anchor: Uri | Location | {
        variableName: string;
        value?: Uri | Location;
    }, iconPath?: (Uri | ThemeIcon | {
        light: Uri;
        dark: Uri;
    }) | undefined, options?: {
        status?: {
            description: string;
            kind: ChatResponseReferencePartStatusKind;
        };
        isFromTool?: boolean;
    } | undefined);
    toJSON(): JSON.PromptReferenceJSON;
}
