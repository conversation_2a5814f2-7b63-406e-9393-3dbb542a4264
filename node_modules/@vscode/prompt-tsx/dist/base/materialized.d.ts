import { Raw } from './output/mode';
import { ToolCall } from './promptElements';
import { MetadataMap } from './promptRenderer';
import { PromptMetadata } from './results';
import { ITokenizer } from './tokenizer/tokenizer';
export interface IMaterializedNode {
    /**
     * Gets the maximum number of tokens this message can contain. This is
     * calculated by summing the token counts of all individual messages, which
     * may be larger than the real count due to merging of sibling tokens.
     */
    upperBoundTokenCount(tokenizer: ITokenizer): Promise<number>;
    /**
     * Gets whether this node has any content to represent to the model.
     */
    readonly isEmpty: boolean;
}
interface IMaterializedContainer extends IMaterializedNode {
    /**
     * Called when children change, so caches can be invalidated.
     */
    onChunksChange(): void;
}
export type MaterializedNode = GenericMaterializedContainer | MaterializedChatMessage | MaterializedChatMessageTextChunk | MaterializedChatMessageImage | MaterializedChatMessageOpaque | MaterializedChatMessageBreakpoint;
export declare const enum ContainerFlags {
    /** It's a {@link LegacyPrioritization} instance */
    IsLegacyPrioritization = 1,
    /** It's a {@link Chunk} instance */
    IsChunk = 2,
    /** Priority is passed to children. */
    PassPriority = 4,
    /** Is an alternate with children  */
    EmptyAlternate = 8
}
type ContainerType = MaterializedChatMessage | GenericMaterializedContainer;
export declare class GenericMaterializedContainer implements IMaterializedContainer {
    readonly parent: ContainerType | undefined;
    readonly id: number;
    readonly name: string | undefined;
    readonly priority: number;
    readonly metadata: PromptMetadata[];
    readonly flags: number;
    readonly children: MaterializedNode[];
    keepWithId?: number;
    constructor(parent: ContainerType | undefined, id: number, name: string | undefined, priority: number, childrenRef: (parent: GenericMaterializedContainer) => MaterializedNode[], metadata: PromptMetadata[], flags: number);
    has(flag: ContainerFlags): boolean;
    /** @inheritdoc */
    tokenCount(tokenizer: ITokenizer): Promise<number>;
    /** @inheritdoc */
    upperBoundTokenCount(tokenizer: ITokenizer): Promise<number>;
    /**
     * Replaces a node in the tree with the given one, by its ID.
     */
    replaceNode(nodeId: number, withNode: MaterializedNode): MaterializedNode | undefined;
    /**
     * Gets all metadata the container holds.
     */
    allMetadata(): Generator<PromptMetadata>;
    /**
     * Finds a node in the tree by ID.
     */
    findById(nodeId: number): ContainerType | undefined;
    /**
     * Gets whether the container is empty.
     */
    get isEmpty(): boolean;
    /**
     * Called when children change, so caches can be invalidated.
     */
    onChunksChange(): void;
    /**
     * Gets the chat messages the container holds.
     */
    toChatMessages(): Generator<Raw.ChatMessage>;
    baseMessageTokenCount(tokenizer: ITokenizer): Promise<number>;
    /**
     * Removes the node in the tree with the lowest priority. Returns the
     * list of nodes that were removed.
     */
    removeLowestPriorityChild(): MaterializedNode[];
}
export declare const enum LineBreakBefore {
    None = 0,
    Always = 1,
    IfNotTextSibling = 2
}
/** A chunk of text in a {@link MaterializedChatMessage} */
export declare class MaterializedChatMessageTextChunk implements IMaterializedNode {
    readonly parent: ContainerType | undefined;
    readonly text: string;
    readonly priority: number;
    readonly metadata: PromptMetadata[];
    readonly lineBreakBefore: LineBreakBefore;
    constructor(parent: ContainerType | undefined, text: string, priority: number, metadata: PromptMetadata[] | undefined, lineBreakBefore: LineBreakBefore);
    upperBoundTokenCount(tokenizer: ITokenizer): Promise<number>;
    private readonly _upperBound;
    get isEmpty(): boolean;
}
export declare class MaterializedChatMessage implements IMaterializedNode {
    readonly parent: ContainerType | undefined;
    readonly id: number;
    readonly role: Raw.ChatRole;
    readonly name: string | undefined;
    toolCalls: readonly ToolCall[] | undefined;
    readonly toolCallId: string | undefined;
    readonly priority: number;
    readonly metadata: PromptMetadata[];
    readonly children: MaterializedNode[];
    constructor(parent: ContainerType | undefined, id: number, role: Raw.ChatRole, name: string | undefined, toolCalls: readonly ToolCall[] | undefined, toolCallId: string | undefined, priority: number, metadata: PromptMetadata[], childrenRef: (parent: MaterializedChatMessage) => MaterializedNode[]);
    /** @inheritdoc */
    tokenCount(tokenizer: ITokenizer): Promise<number>;
    /** @inheritdoc */
    upperBoundTokenCount(tokenizer: ITokenizer): Promise<number>;
    /** Gets the text this message contains */
    get text(): (string | MaterializedChatMessageImage | MaterializedChatMessageOpaque | MaterializedChatMessageBreakpoint)[];
    /** Gets whether the message is empty */
    get isEmpty(): boolean;
    /**
     * Replaces a node in the tree with the given one, by its ID.
     */
    replaceNode(nodeId: number, withNode: MaterializedNode): MaterializedNode | undefined;
    removeLowestPriorityChild(): MaterializedNode[];
    onChunksChange(): void;
    /**
     * Finds a node in the tree by ID.
     */
    findById(nodeId: number): GenericMaterializedContainer | MaterializedChatMessage | MaterializedChatMessageImage | undefined;
    private readonly _tokenCount;
    private readonly _upperBound;
    readonly baseMessageTokenCount: ((tokenizer: ITokenizer) => number | Promise<number>) & {
        clear: () => void;
    };
    private readonly _text;
    toChatMessage(): Raw.ChatMessage;
}
export declare class MaterializedChatMessageOpaque {
    readonly parent: ContainerType | undefined;
    private readonly part;
    readonly metadata: PromptMetadata[];
    readonly priority: number;
    get value(): unknown;
    constructor(parent: ContainerType | undefined, part: Raw.ChatCompletionContentPartOpaque);
    upperBoundTokenCount(tokenizer: ITokenizer): number;
    isEmpty: boolean;
}
export declare class MaterializedChatMessageBreakpoint {
    readonly parent: ContainerType | undefined;
    readonly part: Raw.ChatCompletionContentPartCacheBreakpoint;
    readonly metadata: PromptMetadata[];
    readonly priority: number;
    constructor(parent: ContainerType | undefined, part: Raw.ChatCompletionContentPartCacheBreakpoint);
    upperBoundTokenCount(_tokenizer: ITokenizer): number;
    isEmpty: boolean;
}
export declare class MaterializedChatMessageImage {
    readonly parent: ContainerType | undefined;
    readonly id: number;
    readonly src: string;
    readonly priority: number;
    readonly metadata: PromptMetadata[];
    readonly lineBreakBefore: LineBreakBefore;
    readonly detail?: ("low" | "high") | undefined;
    constructor(parent: ContainerType | undefined, id: number, src: string, priority: number, metadata: PromptMetadata[] | undefined, lineBreakBefore: LineBreakBefore, detail?: ("low" | "high") | undefined);
    upperBoundTokenCount(tokenizer: ITokenizer): Promise<number>;
    private readonly _upperBound;
    isEmpty: boolean;
}
/** Thrown when the TSX budget is exceeded and we can't remove elements to reduce it. */
export declare class BudgetExceededError extends Error {
    metadata: MetadataMap;
    constructor(node: ContainerType);
}
export {};
