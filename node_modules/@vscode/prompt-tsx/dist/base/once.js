"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.once = once;
function once(fn) {
    let result;
    let called = false;
    const wrappedFunction = ((...args) => {
        if (!called) {
            result = fn(...args);
            called = true;
        }
        return result;
    });
    wrappedFunction.clear = () => {
        called = false;
    };
    return wrappedFunction;
}
