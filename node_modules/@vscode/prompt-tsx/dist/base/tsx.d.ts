interface _InternalPromptPiece<P = any> {
    ctor: string | any;
    props: P;
    children: string | (_InternalPromptPiece<any> | undefined)[];
}
/**
 * Visual Studio Code Prompt Piece
 */
declare function _vscpp(ctor: any, props: any, ...children: any[]): _InternalPromptPiece;
/**
 * Visual Studio Code Prompt Piece Fragment
 */
declare function _vscppf(): void;
declare namespace _vscppf {
    var isFragment: boolean;
}
declare const vscpp: typeof _vscpp;
declare const vscppf: typeof _vscppf;
