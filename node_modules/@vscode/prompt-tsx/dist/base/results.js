"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptReference = exports.ChatResponseReferencePartStatusKind = exports.PromptMetadata = void 0;
const uri_1 = require("./util/vs/common/uri");
/**
 * Arbitrary metadata which can be retrieved after the prompt is rendered.
 */
class PromptMetadata {
    _marker;
    toString() {
        return Object.getPrototypeOf(this).constructor.name;
    }
}
exports.PromptMetadata = PromptMetadata;
var ChatResponseReferencePartStatusKind;
(function (ChatResponseReferencePartStatusKind) {
    ChatResponseReferencePartStatusKind[ChatResponseReferencePartStatusKind["Complete"] = 1] = "Complete";
    ChatResponseReferencePartStatusKind[ChatResponseReferencePartStatusKind["Partial"] = 2] = "Partial";
    ChatResponseReferencePartStatusKind[ChatResponseReferencePartStatusKind["Omitted"] = 3] = "Omitted";
})(ChatResponseReferencePartStatusKind || (exports.ChatResponseReferencePartStatusKind = ChatResponseReferencePartStatusKind = {}));
/**
 * A reference used for creating the prompt.
 */
class PromptReference {
    anchor;
    iconPath;
    options;
    static fromJSON(json) {
        // todo@connor4312: do we need to create concrete Location/Range types?
        const uriOrLocation = (v) => 'scheme' in v ? uri_1.URI.from(v) : { uri: uri_1.URI.from(v.uri), range: v.range };
        return new PromptReference('variableName' in json.anchor
            ? {
                variableName: json.anchor.variableName,
                value: json.anchor.value && uriOrLocation(json.anchor.value),
            }
            : uriOrLocation(json.anchor), json.iconPath &&
            ('scheme' in json.iconPath
                ? uri_1.URI.from(json.iconPath)
                : 'light' in json.iconPath
                    ? { light: uri_1.URI.from(json.iconPath.light), dark: uri_1.URI.from(json.iconPath.dark) }
                    : json.iconPath), json.options);
    }
    constructor(anchor, iconPath, options) {
        this.anchor = anchor;
        this.iconPath = iconPath;
        this.options = options;
    }
    toJSON() {
        return {
            anchor: this.anchor,
            iconPath: this.iconPath,
            options: this.options,
        };
    }
}
exports.PromptReference = PromptReference;
