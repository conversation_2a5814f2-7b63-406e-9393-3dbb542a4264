"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.jsonRetainedProps = void 0;
exports.forEachNode = forEachNode;
exports.jsonRetainedProps = Object.keys({
    flexBasis: 1,
    flexGrow: 1,
    flexReserve: 1,
    passPriority: 1,
    priority: 1,
});
/** Iterates over each {@link PromptNodeJSON} in the tree. */
function forEachNode(node, fn) {
    fn(node);
    if (node.type === 1 /* PromptNodeType.Piece */) {
        for (const child of node.children) {
            forEachNode(child, fn);
        }
    }
}
