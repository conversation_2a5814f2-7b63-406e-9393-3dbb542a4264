"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCompletionContentPartOpaque = exports.ChatCompletionContentPartKind = exports.ChatRole = void 0;
const assert_1 = require("../util/assert");
/**
 * The role of a message in an OpenAI completions request.
 */
var ChatRole;
(function (ChatRole) {
    ChatRole[ChatRole["System"] = 0] = "System";
    ChatRole[ChatRole["User"] = 1] = "User";
    ChatRole[ChatRole["Assistant"] = 2] = "Assistant";
    ChatRole[ChatRole["Tool"] = 3] = "Tool";
})(ChatRole || (exports.ChatRole = ChatRole = {}));
(function (ChatRole) {
    function display(role) {
        switch (role) {
            case ChatRole.System:
                return 'system';
            case ChatRole.User:
                return 'user';
            case ChatRole.Assistant:
                return 'assistant';
            case ChatRole.Tool:
                return 'tool';
            default:
                (0, assert_1.assertNever)(role, `unknown chat role ${role}}`);
        }
    }
    ChatRole.display = display;
})(ChatRole || (exports.ChatRole = ChatRole = {}));
var ChatCompletionContentPartKind;
(function (ChatCompletionContentPartKind) {
    ChatCompletionContentPartKind[ChatCompletionContentPartKind["Image"] = 0] = "Image";
    ChatCompletionContentPartKind[ChatCompletionContentPartKind["Text"] = 1] = "Text";
    ChatCompletionContentPartKind[ChatCompletionContentPartKind["Opaque"] = 2] = "Opaque";
    ChatCompletionContentPartKind[ChatCompletionContentPartKind["CacheBreakpoint"] = 3] = "CacheBreakpoint";
})(ChatCompletionContentPartKind || (exports.ChatCompletionContentPartKind = ChatCompletionContentPartKind = {}));
var ChatCompletionContentPartOpaque;
(function (ChatCompletionContentPartOpaque) {
    function usableIn(part, mode) {
        return !part.scope || (part.scope & mode) !== 0;
    }
    ChatCompletionContentPartOpaque.usableIn = usableIn;
})(ChatCompletionContentPartOpaque || (exports.ChatCompletionContentPartOpaque = ChatCompletionContentPartOpaque = {}));
