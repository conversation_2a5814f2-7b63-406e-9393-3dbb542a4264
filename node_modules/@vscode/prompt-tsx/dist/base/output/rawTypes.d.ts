import type { OutputMode } from './mode';
/**
 * A chat message emitted by this library. This can be mapped to other APIs
 * easily using {@link toMode}.
 *
 * Please note:
 * - Enumerations and union types are non-exhaustive. More types may be added
 *   in the future.
 * - Data in this representation is very permissible and converting to API
 *   representations may be lossy.
 */
export type ChatMessage = AssistantChatMessage | SystemChatMessage | UserChatMessage | ToolChatMessage;
/**
 * The role of a message in an OpenAI completions request.
 */
export declare enum ChatRole {
    System = 0,
    User = 1,
    Assistant = 2,
    Tool = 3
}
export declare namespace ChatRole {
    function display(role: ChatRole): string;
}
export interface BaseChatMessage {
    role: ChatRole;
    content: ChatCompletionContentPart[];
    /**
     * An optional name for the participant. Provides the model information to differentiate between participants of the same role.
     */
    name?: string;
}
export interface SystemChatMessage extends BaseChatMessage {
    role: ChatRole.System;
}
export interface UserChatMessage extends BaseChatMessage {
    role: ChatRole.User;
}
export type ChatCompletionContentPart = ChatCompletionContentPartImage | ChatCompletionContentPartText | ChatCompletionContentPartOpaque | ChatCompletionContentPartCacheBreakpoint;
export declare enum ChatCompletionContentPartKind {
    Image = 0,
    Text = 1,
    Opaque = 2,
    CacheBreakpoint = 3
}
/** An image completion */
export interface ChatCompletionContentPartImage {
    imageUrl: ImageURLReference;
    type: ChatCompletionContentPartKind.Image;
}
export interface ChatCompletionContentPartCacheBreakpoint {
    type: ChatCompletionContentPartKind.CacheBreakpoint;
    /**
     * Optional implementation-specific type of the breakpoint.
     */
    cacheType?: string;
}
export interface ImageURLReference {
    /**
     * Either a URL of the image or the base64 encoded image data.
     */
    url: string;
    /**
     * Specifies the detail level of the image. Learn more in the
     * [Vision guide](https://platform.openai.com/docs/guides/vision/low-or-high-fidelity-image-understanding).
     */
    detail?: 'low' | 'high';
}
export interface ChatCompletionContentPartText {
    /**
     * The text content.
     */
    text: string;
    /**
     * The type of the content part.
     */
    type: ChatCompletionContentPartKind.Text;
}
export interface ChatCompletionContentPartOpaque {
    /**
     * A JSON-stringifiable value
     */
    value: unknown;
    /**
     * Constant-value token usage of this content part. If undefined, it will
     * be assumed 0.
     */
    tokenUsage?: number;
    /**
     * A bitset of output modes where this content part will be omitted.
     * E.g. `scope: OutputMode.Anthropic | OutputMode.VSCode`. Not all outputs
     * will support opaque parts everywhere.
     */
    scope?: number;
    /**
     * The type of the content part.
     */
    type: ChatCompletionContentPartKind.Opaque;
}
export declare namespace ChatCompletionContentPartOpaque {
    function usableIn(part: ChatCompletionContentPartOpaque, mode: OutputMode): boolean;
}
export interface ChatMessageToolCall {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The function that the model called.
     */
    function: ChatMessageFunction;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
export interface AssistantChatMessage extends BaseChatMessage {
    role: ChatRole.Assistant;
    /**
     * An optional name for the participant. Provides the model information to differentiate between participants of the same role.
     */
    name?: string;
    /**
     * The tool calls generated by the model.
     */
    toolCalls?: ChatMessageToolCall[];
}
export interface ToolChatMessage extends BaseChatMessage {
    role: ChatRole.Tool;
    /**
     * Tool call that this message is responding to.
     */
    toolCallId: string;
}
/**
 * The function that the model called.
 */
export interface ChatMessageFunction {
    /**
     * The arguments to call the function with, as generated by the model in JSON
     * format.
     */
    arguments: string;
    /**
     * The name of the function to call.
     */
    name: string;
}
