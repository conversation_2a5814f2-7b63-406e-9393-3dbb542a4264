"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutputMode = exports.Raw = exports.OpenAI = void 0;
exports.toMode = toMode;
exports.toVSCode = toVSCode;
exports.toOpenAI = toOpenAI;
const openaiConvert_1 = require("./openaiConvert");
const vscode_1 = require("./vscode");
exports.OpenAI = require("./openaiTypes");
exports.Raw = require("./rawTypes");
var OutputMode;
(function (OutputMode) {
    OutputMode[OutputMode["Raw"] = 1] = "Raw";
    OutputMode[OutputMode["OpenAI"] = 2] = "OpenAI";
    OutputMode[OutputMode["VSCode"] = 4] = "VSCode";
})(OutputMode || (exports.OutputMode = OutputMode = {}));
function toMode(mode, messages) {
    switch (mode) {
        case OutputMode.Raw:
            return messages;
        case OutputMode.VSCode:
            return (messages instanceof Array ? (0, vscode_1.toVsCodeChatMessages)(messages) : (0, vscode_1.toVsCodeChatMessage)(messages));
        case OutputMode.OpenAI:
            return (messages instanceof Array ? (0, openaiConvert_1.toOpenAIChatMessages)(messages) : (0, openaiConvert_1.toOpenAiChatMessage)(messages));
        default:
            throw new Error(`Unknown output mode: ${mode}`);
    }
}
function toVSCode(messages) {
    return toMode(OutputMode.VSCode, messages);
}
function toOpenAI(messages) {
    return toMode(OutputMode.OpenAI, messages);
}
