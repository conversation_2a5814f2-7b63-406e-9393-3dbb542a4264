"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toOpenAiChatMessage = toOpenAiChatMessage;
exports.toOpenAIChatMessages = toOpenAIChatMessages;
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
const Raw = require("./rawTypes");
const OpenAI = require("./openaiTypes");
const mode_1 = require("./mode");
function onlyStringContent(content) {
    return content
        .filter(part => part.type === Raw.ChatCompletionContentPartKind.Text)
        .map(part => part.text)
        .join('');
}
function stringAndImageContent(content) {
    const parts = content
        .map((part) => {
        if (part.type === Raw.ChatCompletionContentPartKind.Text) {
            return {
                type: 'text',
                text: part.text,
            };
        }
        else if (part.type === Raw.ChatCompletionContentPartKind.Image) {
            return {
                image_url: part.imageUrl,
                type: 'image_url',
            };
        }
        else if (part.type === Raw.ChatCompletionContentPartKind.Opaque &&
            Raw.ChatCompletionContentPartOpaque.usableIn(part, mode_1.OutputMode.OpenAI)) {
            return part;
        }
    })
        .filter(r => !!r);
    if (parts.every(part => part.type === 'text')) {
        return parts.map(p => p.text).join('');
    }
    return parts;
}
function toOpenAiChatMessage(message) {
    switch (message.role) {
        case Raw.ChatRole.System:
            return {
                role: OpenAI.ChatRole.System,
                content: onlyStringContent(message.content),
                name: message.name,
            };
        case Raw.ChatRole.User:
            return {
                role: OpenAI.ChatRole.User,
                content: stringAndImageContent(message.content),
                name: message.name,
            };
        case Raw.ChatRole.Assistant:
            return {
                role: OpenAI.ChatRole.Assistant,
                content: onlyStringContent(message.content),
                name: message.name,
                tool_calls: message.toolCalls?.map(toolCall => ({
                    id: toolCall.id,
                    function: toolCall.function,
                    type: 'function',
                })),
            };
        case Raw.ChatRole.Tool:
            return {
                role: OpenAI.ChatRole.Tool,
                content: stringAndImageContent(message.content),
                tool_call_id: message.toolCallId,
            };
        default:
            return undefined;
    }
}
function toOpenAIChatMessages(messages) {
    return messages.map(toOpenAiChatMessage).filter(r => !!r);
}
