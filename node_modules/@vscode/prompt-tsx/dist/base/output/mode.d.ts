import type { LanguageModelChatMessage } from 'vscode';
import { ChatMessage as OpenAIChatMessage } from './openaiTypes';
import { ChatMessage as RawChatMessage } from './rawTypes';
export * as OpenAI from './openaiTypes';
export * as Raw from './rawTypes';
export declare enum OutputMode {
    Raw = 1,
    OpenAI = 2,
    VSCode = 4
}
/** Map of the mode to the type of message it produces. */
export interface ModeToChatMessageType {
    [OutputMode.Raw]: RawChatMessage;
    [OutputMode.VSCode]: LanguageModelChatMessage;
    [OutputMode.OpenAI]: OpenAIChatMessage;
}
/**
 * Converts the raw message representation emitted by this library to the given
 * type of chat message. The target chat message may or may not represent all
 * data included in the {@link RawChatMessage}.
 */
export declare function toMode<Mode extends keyof ModeToChatMessageType>(mode: Mode, messages: RawChatMessage): ModeToChatMessageType[Mode];
export declare function toMode<Mode extends keyof ModeToChatMessageType>(mode: Mode, messages: readonly RawChatMessage[]): ModeToChatMessageType[Mode][];
export declare function toVSCode(messages: RawChatMessage): LanguageModelChatMessage;
export declare function toVSCode(messages: readonly RawChatMessage[]): LanguageModelChatMessage[];
export declare function toOpenAI(messages: RawChatMessage): OpenAIChatMessage;
export declare function toOpenAI(messages: readonly RawChatMessage[]): OpenAIChatMessage[];
