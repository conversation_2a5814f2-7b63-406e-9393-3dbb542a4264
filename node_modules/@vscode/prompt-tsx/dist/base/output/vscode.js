"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toVsCodeChatMessage = toVsCodeChatMessage;
exports.toVsCodeChatMessages = toVsCodeChatMessages;
const Raw = require("./rawTypes");
function onlyStringContent(content) {
    return content
        .filter(part => part.type === Raw.ChatCompletionContentPartKind.Text)
        .map(part => part.text)
        .join('');
}
let vscode;
function toVsCodeChatMessage(m) {
    vscode ??= require('vscode');
    switch (m.role) {
        case Raw.ChatRole.Assistant:
            const message = vscode.LanguageModelChatMessage.Assistant(onlyStringContent(m.content), m.name);
            if (m.toolCalls) {
                message.content = [
                    new vscode.LanguageModelTextPart(onlyStringContent(m.content)),
                    ...m.toolCalls.map(tc => {
                        // prompt-tsx got args passed as a string, here we assume they are JSON because the vscode-type wants an object
                        let parsedArgs;
                        try {
                            parsedArgs = JSON.parse(tc.function.arguments);
                        }
                        catch (err) {
                            throw new Error('Invalid JSON in tool call arguments for tool call: ' + tc.id);
                        }
                        return new vscode.LanguageModelToolCallPart(tc.id, tc.function.name, parsedArgs);
                    }),
                ];
            }
            return message;
        case Raw.ChatRole.User:
            return vscode.LanguageModelChatMessage.User(onlyStringContent(m.content), m.name);
        case Raw.ChatRole.Tool: {
            const message = vscode.LanguageModelChatMessage.User('');
            message.content = [
                new vscode.LanguageModelToolResultPart(m.toolCallId, [
                    new vscode.LanguageModelTextPart(onlyStringContent(m.content)),
                ]),
            ];
            return message;
        }
        default:
            return undefined;
    }
}
/**
 * Converts an array of {@link ChatMessage} objects to an array of corresponding {@link LanguageModelChatMessage VS Code chat messages}.
 * @param messages - The array of {@link ChatMessage} objects to convert.
 * @returns An array of {@link LanguageModelChatMessage VS Code chat messages}.
 */
function toVsCodeChatMessages(messages) {
    return messages.map(toVsCodeChatMessage).filter(r => !!r);
}
