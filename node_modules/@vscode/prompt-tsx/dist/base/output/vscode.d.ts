import type * as vscodeType from 'vscode';
import * as Raw from './rawTypes';
export declare function toVsCodeChatMessage(m: Raw.ChatMessage): vscodeType.LanguageModelChatMessage | undefined;
/**
 * Converts an array of {@link ChatMessage} objects to an array of corresponding {@link LanguageModelChatMessage VS Code chat messages}.
 * @param messages - The array of {@link ChatMessage} objects to convert.
 * @returns An array of {@link LanguageModelChatMessage VS Code chat messages}.
 */
export declare function toVsCodeChatMessages(messages: readonly Raw.ChatMessage[]): vscodeType.LanguageModelChatMessage[];
