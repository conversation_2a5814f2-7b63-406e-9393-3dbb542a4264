/**
 * An OpenAI Chat Completion message.
 *
 * Reference: https://platform.openai.com/docs/api-reference/chat/create
 */
export type ChatMessage = AssistantChatMessage | SystemChatMessage | UserChatMessage | ToolChatMessage | FunctionChatMessage;
export interface SystemChatMessage {
    role: ChatRole.System;
    /**
     * The content of the chat message.
     */
    content: string;
    /**
     * An optional name for the participant. Provides the model information to differentiate between participants of the same role.
     */
    name?: string;
}
export interface UserChatMessage {
    role: ChatRole.User;
    /**
     * The content of the chat message.
     */
    content: string | Array<ChatCompletionContentPart>;
    /**
     * An optional name for the participant. Provides the model information to differentiate between participants of the same role.
     */
    name?: string;
}
export type ChatCompletionContentPart = ChatCompletionContentPartImage | ChatCompletionContentPartText;
export interface ChatCompletionContentPartImage {
    image_url: ChatCompletionContentPartImage.ImageURL;
    /**
     * The type of the content part.
     */
    type: 'image_url';
}
export declare namespace ChatCompletionContentPartImage {
    interface ImageURL {
        /**
         * Either a URL of the image or the base64 encoded image data.
         */
        url: string;
        /**
         * Specifies the detail level of the image. Learn more in the
         * [Vision guide](https://platform.openai.com/docs/guides/vision/low-or-high-fidelity-image-understanding).
         */
        detail?: 'low' | 'high';
    }
}
export interface ChatCompletionContentPartText {
    /**
     * The text content.
     */
    text: string;
    /**
     * The type of the content part.
     */
    type: 'text';
}
export interface ChatMessageToolCall {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The function that the model called.
     */
    function: ChatMessageFunction;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
export interface AssistantChatMessage {
    role: ChatRole.Assistant;
    /**
     * The content of the chat message.
     */
    content: string;
    /**
     * An optional name for the participant. Provides the model information to differentiate between participants of the same role.
     */
    name?: string;
    /**
     * The tool calls generated by the model.
     */
    tool_calls?: Array<ChatMessageToolCall>;
}
export interface ToolChatMessage {
    role: ChatRole.Tool;
    /**
     * Tool call that this message is responding to.
     */
    tool_call_id?: string;
    /**
     * The content of the chat message.
     */
    content: string | Array<ChatCompletionContentPart>;
}
/**
 * @deprecated Use {@link ToolChatMessage} instead.
 */
export interface FunctionChatMessage {
    role: ChatRole.Function;
    /**
     * The content of the chat message.
     */
    content: string;
    /**
     * The name of the function that was called
     */
    name: string;
}
/**
 * The function that the model called.
 */
export interface ChatMessageFunction {
    /**
     * The arguments to call the function with, as generated by the model in JSON
     * format. Note that the model does not always generate valid JSON, and may
     * hallucinate parameters not defined by your function schema. Validate the
     * arguments in your code before calling your function.
     */
    arguments: string;
    /**
     * The name of the function to call.
     */
    name: string;
}
/**
 * The role of a message in an OpenAI completions request.
 */
export declare enum ChatRole {
    System = "system",
    User = "user",
    Assistant = "assistant",
    Function = "function",
    Tool = "tool"
}
/**
 * BaseTokensPerCompletion is the minimum tokens for a completion request.
 * Replies are primed with <|im_start|>assistant<|message|>, so these tokens represent the
 * special token and the role name.
 */
export declare const BaseTokensPerCompletion = 3;
export declare const BaseTokensPerMessage = 3;
export declare const BaseTokensPerName = 1;
