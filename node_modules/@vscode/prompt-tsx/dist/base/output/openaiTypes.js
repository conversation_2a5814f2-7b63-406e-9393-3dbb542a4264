"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTokensPerName = exports.BaseTokensPerMessage = exports.BaseTokensPerCompletion = exports.ChatRole = void 0;
/**
 * The role of a message in an OpenAI completions request.
 */
var ChatRole;
(function (ChatRole) {
    ChatRole["System"] = "system";
    ChatRole["User"] = "user";
    Chat<PERSON><PERSON>["Assistant"] = "assistant";
    ChatRole["Function"] = "function";
    ChatRole["Tool"] = "tool";
})(ChatRole || (exports.ChatRole = ChatRole = {}));
/**
 * BaseTokensPerCompletion is the minimum tokens for a completion request.
 * Replies are primed with <|im_start|>assistant<|message|>, so these tokens represent the
 * special token and the role name.
 */
exports.BaseTokensPerCompletion = 3;
/*
 * Each GPT 3.5 / GPT 4 message comes with 3 tokens per message due to special characters
 */
exports.BaseTokensPerMessage = 3;
/*
 * Since gpt-3.5-turbo-0613 each name costs 1 token
 */
exports.BaseTokensPerName = 1;
