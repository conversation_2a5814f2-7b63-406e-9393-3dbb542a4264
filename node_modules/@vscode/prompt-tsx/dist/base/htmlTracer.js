"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTMLTracer = void 0;
const htmlTracerSrc_1 = require("./htmlTracerSrc");
const materialized_1 = require("./materialized");
const mode_1 = require("./output/mode");
/**
 * Handler that can trace rendering internals into an HTML summary.
 */
class HTMLTracer {
    traceData;
    epochs = [];
    addRenderEpoch(epoch) {
        this.epochs.push(epoch);
    }
    includeInEpoch(data) {
        this.epochs[this.epochs.length - 1].elements.push(data);
    }
    didMaterializeTree(traceData) {
        this.traceData = traceData;
    }
    /**
     * Returns HTML to trace the output. Note that is starts a server which is
     * used for client interaction to resize the prompt and its `address` should
     * be displayed or opened as a link in a browser.
     *
     * The server runs until it is disposed.
     */
    async serveHTML() {
        return RequestServer.create({
            epochs: this.epochs,
            traceData: mustGet(this.traceData),
        });
    }
    /**
     * Gets an HTML router for a server at the URL. URL is the form `http://127.0.0.1:1234`.
     */
    serveRouter(url) {
        return new RequestRouter({
            baseAddress: url,
            epochs: this.epochs,
            traceData: mustGet(this.traceData),
        });
    }
}
exports.HTMLTracer = HTMLTracer;
class RequestRouter {
    opts;
    serverToken = crypto.randomUUID();
    constructor(opts) {
        this.opts = opts;
    }
    route(httpIncomingMessage, httpOutgoingMessage) {
        const req = httpIncomingMessage;
        const res = httpOutgoingMessage;
        const url = new URL(req.url || '/', `http://localhost`);
        const prefix = `/${this.serverToken}`;
        switch (url.pathname) {
            case prefix:
            case `${prefix}/`:
                this.onRoot(url, req, res);
                break;
            case `${prefix}/regen`:
                this.onRegen(url, req, res);
                break;
            default:
                return false;
        }
        return true;
    }
    get address() {
        return this.opts.baseAddress + '/' + this.serverToken;
    }
    async getHTML() {
        const { traceData, epochs } = this.opts;
        return `<body>
			<style>${htmlTracerSrc_1.tracerCss}</style>
			<script>
				const DEFAULT_TOKENS = ${JSON.stringify(traceData.budget)};
				const EPOCHS = ${JSON.stringify(epochs)};
				const DEFAULT_MODEL = ${JSON.stringify(await serializeRenderData(traceData.tokenizer, traceData.renderedTree))};
				const SERVER_ADDRESS = ${JSON.stringify(this.opts.baseAddress + '/' + this.serverToken + '/')};
				${htmlTracerSrc_1.tracerSrc}
			</script>
		</body>`;
    }
    async onRegen(url, _req, res) {
        const { traceData } = this.opts;
        const budget = Number(url.searchParams.get('n') || traceData.budget);
        const renderedTree = await traceData.renderTree(budget);
        const serialized = await serializeRenderData(traceData.tokenizer, renderedTree);
        const json = JSON.stringify(serialized);
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Length', Buffer.byteLength(json));
        res.end(json);
    }
    onRoot(_url, _req, res) {
        this.getHTML().then(html => {
            res.setHeader('Content-Type', 'text/html');
            res.setHeader('Content-Length', Buffer.byteLength(html));
            res.end(html);
        });
    }
}
class RequestServer extends RequestRouter {
    server;
    static async create(opts) {
        const { createServer } = await Promise.resolve().then(() => require('http'));
        const server = createServer((req, res) => {
            try {
                if (!instance.route(req, res)) {
                    res.statusCode = 404;
                    res.end('Not Found');
                }
            }
            catch (e) {
                res.statusCode = 500;
                res.end(String(e));
            }
        });
        const port = await new Promise((resolve, reject) => {
            server
                .listen(0, '127.0.0.1', () => resolve(server.address().port))
                .on('error', reject);
        });
        const instance = new RequestServer({
            ...opts,
            baseAddress: `http://127.0.0.1:${port}`,
        }, server);
        return instance;
    }
    constructor(opts, server) {
        super(opts);
        this.server = server;
    }
    dispose() {
        this.server.closeAllConnections();
        this.server.close();
    }
}
async function serializeRenderData(tokenizer, tree) {
    return {
        container: (await serializeMaterialized(tokenizer, tree.container, false)),
        removed: tree.removed,
        budget: tree.budget,
    };
}
async function serializeMaterialized(tokenizer, materialized, inChatMessage) {
    const common = {
        metadata: materialized.metadata.map(serializeMetadata),
        priority: materialized.priority,
    };
    if (materialized instanceof materialized_1.MaterializedChatMessageTextChunk) {
        return {
            ...common,
            type: 2 /* TraceMaterializedNodeType.TextChunk */,
            value: materialized.text,
            tokens: await materialized.upperBoundTokenCount(tokenizer),
        };
    }
    else if (materialized instanceof materialized_1.MaterializedChatMessageImage) {
        return {
            ...common,
            name: materialized.id.toString(),
            id: materialized.id,
            type: 3 /* TraceMaterializedNodeType.Image */,
            value: materialized.src,
            tokens: await materialized.upperBoundTokenCount(tokenizer),
        };
    }
    else if (materialized instanceof materialized_1.MaterializedChatMessageOpaque ||
        materialized instanceof materialized_1.MaterializedChatMessageBreakpoint) {
        // todo: add to visualizer
        return undefined;
    }
    else {
        const containerCommon = {
            ...common,
            id: materialized.id,
            name: materialized.name,
            children: (await Promise.all(materialized.children.map(c => serializeMaterialized(tokenizer, c, inChatMessage || materialized instanceof materialized_1.MaterializedChatMessage)))).filter(r => !!r),
            tokens: inChatMessage
                ? await materialized.upperBoundTokenCount(tokenizer)
                : await materialized.tokenCount(tokenizer),
        };
        if (materialized instanceof materialized_1.GenericMaterializedContainer) {
            return {
                ...containerCommon,
                type: 0 /* TraceMaterializedNodeType.Container */,
            };
        }
        else if (materialized instanceof materialized_1.MaterializedChatMessage) {
            const content = materialized.text
                .filter(element => typeof element === 'string')
                .join('')
                .trim();
            return {
                ...containerCommon,
                type: 1 /* TraceMaterializedNodeType.ChatMessage */,
                role: mode_1.Raw.ChatRole.display(materialized.role),
                text: content,
            };
        }
    }
    assertNever(materialized);
}
function assertNever(x) {
    throw new Error('unreachable');
}
function serializeMetadata(metadata) {
    return { name: metadata.constructor.name, value: JSON.stringify(metadata) };
}
const mustGet = (value) => {
    if (value === undefined) {
        throw new Error('Prompt must be rendered before calling HTMLTRacer.serveHTML');
    }
    return value;
};
