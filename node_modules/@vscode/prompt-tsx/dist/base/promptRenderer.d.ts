import type { CancellationToken, Progress } from 'vscode';
import * as JSONT from './jsonTypes';
import { PromptNodeType } from './jsonTypes';
import { GenericMaterializedContainer, MaterializedChatMessage, MaterializedChatMessageImage } from './materialized';
import { ModeToChatMessageType, OutputMode } from './output/mode';
import { PromptElement } from './promptElement';
import { KeepWithCtor } from './promptElements';
import { PromptMetadata, PromptReference } from './results';
import { ITokenizer } from './tokenizer/tokenizer';
import { ITracer } from './tracer';
import { BasePromptElementProps, IChatEndpointInfo, PromptElementCtor, PromptPieceChild } from './types';
import { URI } from './util/vs/common/uri';
import { ChatDocumentContext, ChatResponsePart } from './vscodeTypes';
export interface RenderPromptResult<M extends OutputMode = OutputMode.Raw> {
    readonly messages: ModeToChatMessageType[M][];
    readonly tokenCount: number;
    readonly hasIgnoredFiles: boolean;
    readonly metadata: MetadataMap;
    /**
     * The references that survived prioritization in the rendered {@link RenderPromptResult.messages messages}.
     */
    readonly references: PromptReference[];
    /**
     * The references attached to chat message chunks that did not survive prioritization.
     */
    readonly omittedReferences: PromptReference[];
}
export type QueueItem<C, P> = {
    path: (PromptElementCtor<any, any> | string)[];
    node: PromptTreeElement;
    ctor: C;
    props: P;
    children: PromptPieceChild[];
};
export interface MetadataMap {
    get<T extends PromptMetadata>(key: new (...args: any[]) => T): T | undefined;
    getAll<T extends PromptMetadata>(key: new (...args: any[]) => T): T[];
}
export declare namespace MetadataMap {
    const empty: MetadataMap;
    const from: (metadata: PromptMetadata[]) => MetadataMap;
}
/**
 * A prompt renderer is responsible for rendering a {@link PromptElementCtor prompt element} to {@link ChatMessagePromptElement chat messages}.
 *
 * Note: You must create a fresh prompt renderer instance for each prompt element you want to render.
 */
export declare class PromptRenderer<P extends BasePromptElementProps, M extends OutputMode> {
    private readonly _endpoint;
    private readonly _ctor;
    private readonly _props;
    private readonly _tokenizer;
    private readonly _usedContext;
    private readonly _ignoredFiles;
    private readonly _growables;
    private readonly _root;
    private readonly _tokenLimits;
    /** Epoch used to tracing the order in which elements render. */
    tracer: ITracer | undefined;
    /**
     * @param _endpoint The chat endpoint that the rendered prompt will be sent to.
     * @param _ctor The prompt element constructor to render.
     * @param _props The props to pass to the prompt element.
     */
    constructor(_endpoint: IChatEndpointInfo, _ctor: PromptElementCtor<P, any>, _props: P, _tokenizer: ITokenizer<M>);
    getIgnoredFiles(): URI[];
    getUsedContext(): ChatDocumentContext[];
    protected createElement(element: QueueItem<PromptElementCtor<P, any>, P>): PromptElement<P, any>;
    private _processPromptPieces;
    private _processPromptRenderPiece;
    /**
     * Renders the prompt element and its children to a JSON-serializable state.
     * @returns A promise that resolves to an object containing the rendered chat messages and the total token count.
     * The total token count is guaranteed to be less than or equal to the token budget.
     */
    renderElementJSON(token?: CancellationToken): Promise<JSONT.PromptElementJSON>;
    /**
     * Renders the prompt element and its children.
     * @returns A promise that resolves to an object containing the rendered chat messages and the total token count.
     * The total token count is guaranteed to be less than or equal to the token budget.
     */
    render(progress?: Progress<ChatResponsePart>, token?: CancellationToken): Promise<RenderPromptResult<M>>;
    /**
     * Renders the prompt element and its children. Similar to {@link render}, but
     * returns the original message representation.
     */
    renderRaw(progress?: Progress<ChatResponsePart>, token?: CancellationToken): Promise<RenderPromptResult<OutputMode.Raw>>;
    /**
     * Note: this may be called multiple times from the tracer as users play
     * around with budgets. It should be side-effect-free.
     */
    private _getFinalElementTree;
    /** Grows all Expandable elements, returns if any changes were made. */
    private _grow;
    private _handlePromptChildren;
    private _handleIntrinsic;
    private _handleIntrinsicCacheBreakpoint;
    private _handleIntrinsicMeta;
    private _handleIntrinsicLineBreak;
    private _handleIntrinsicElementJSON;
    private _handleIntrinsicUsedContext;
    private _handleIntrinsicReferences;
    private _handleIntrinsicIgnoredFiles;
    /**
     * @param node Parent of the <TextChunk />
     * @param textChunkNode The <TextChunk /> node. All children are in-order
     * appended to the parent using the same sort index to ensure order is preserved.
     * @param props Props of the <TextChunk />
     * @param children Rendered children of the <TextChunk />
     */
    private _handleExtrinsicTextChunkChildren;
}
declare class PromptTreeElement {
    readonly parent: PromptTreeElement | null;
    readonly childIndex: number;
    readonly id: number;
    private static _nextId;
    static fromJSON(index: number, json: JSONT.PieceJSON, keepWithMap: Map<number, KeepWithCtor>): PromptTreeElement;
    readonly kind = PromptNodeType.Piece;
    private _obj;
    private _state;
    private _children;
    private _metadata;
    private _objFlags;
    constructor(parent: (PromptTreeElement | null) | undefined, childIndex: number, id?: number);
    setObj(obj: PromptElement): void;
    /** @deprecated remove when Expandable is gone */
    getObj(): PromptElement | null;
    setState(state: any): void;
    getState(): any;
    createChild(): PromptTreeElement;
    appendPieceJSON(data: JSONT.PieceJSON): PromptTreeElement;
    appendStringChild(text: string, priority?: number, metadata?: PromptMetadata[], sortIndex?: number, lineBreakBefore?: boolean): void;
    appendLineBreak(priority?: number, sortIndex?: number): void;
    toJSON(): JSONT.PieceJSON;
    materialize(parent?: MaterializedChatMessage | GenericMaterializedContainer): MaterializedChatMessage | GenericMaterializedContainer | MaterializedChatMessageImage;
    addMetadata(metadata: PromptMetadata): void;
    addCacheBreakpoint(breakpoint: {
        type: string;
    }, sortIndex?: number): void;
    elements(): Iterable<PromptTreeElement>;
}
export {};
