"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptElement = void 0;
require("./tsx");
/**
 * `PromptElement` represents a single element of a prompt.
 * A prompt element can be rendered by the {@link PromptRenderer} to produce {@link ChatMessage} chat messages.
 *
 * @remarks Newlines are not preserved in string literals when rendered, and must be explicitly declared with the builtin `<br />` attribute.
 *
 * @template P - The type of the properties for the prompt element. It extends `BasePromptElementProps`.
 * @template S - The type of the state for the prompt element. It defaults to `void`.
 *
 * @property props - The properties of the prompt element.
 * @property priority - The priority of the prompt element. If not provided, defaults to 0.
 *
 * @method prepare - Optionally prepares asynchronous state before the prompt element is rendered.
 * @method render - Renders the prompt element. This method is abstract and must be implemented by subclasses.
 */
class PromptElement {
    props;
    get priority() {
        return this.props.priority ?? Number.MAX_SAFE_INTEGER;
    }
    get insertLineBreakBefore() {
        return true;
    }
    constructor(props) {
        this.props = props;
    }
}
exports.PromptElement = PromptElement;
