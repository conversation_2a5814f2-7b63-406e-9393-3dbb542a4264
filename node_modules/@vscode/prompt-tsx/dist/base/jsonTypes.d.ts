import type { Range } from 'vscode';
import { ChatResponseReferencePartStatusKind } from './results';
import { UriComponents } from './util/vs/common/uri';
import { BasePromptElementProps } from './types';
export declare const enum PromptNodeType {
    Piece = 1,
    Text = 2
}
export interface TextJSON {
    type: PromptNodeType.Text;
    text: string;
    priority: number | undefined;
    references: PromptReferenceJSON[] | undefined;
    lineBreakBefore: boolean | undefined;
}
/**
 * Constructor kind of the node represented by {@link PieceJSON}. This is
 * less descriptive than the actual constructor, as we only care to preserve
 * the element data that the renderer cares about.
 */
export declare const enum PieceCtorKind {
    BaseChatMessage = 1,
    Other = 2,
    ImageChatMessage = 3
}
export declare const jsonRetainedProps: readonly (keyof BasePromptElementProps)[];
export interface BasePieceJSON {
    type: PromptNodeType.Piece;
    ctor: PieceCtorKind.BaseChatMessage | PieceCtorKind.Other;
    ctorName: string | undefined;
    children: PromptNodeJSON[];
    references: PromptReferenceJSON[] | undefined;
    props: Record<string, unknown>;
    keepWithId?: number;
    flags?: number;
}
export interface ImageChatMessagePieceJSON {
    type: PromptNodeType.Piece;
    ctor: PieceCtorKind.ImageChatMessage;
    children: PromptNodeJSON[];
    references: PromptReferenceJSON[] | undefined;
    props: {
        src: string;
        detail?: 'low' | 'high';
    };
}
export type PieceJSON = BasePieceJSON | ImageChatMessagePieceJSON;
export type PromptNodeJSON = PieceJSON | TextJSON;
export type UriOrLocationJSON = UriComponents | {
    uri: UriComponents;
    range: Range;
};
export interface PromptReferenceJSON {
    anchor: UriOrLocationJSON | {
        variableName: string;
        value?: UriOrLocationJSON;
    };
    iconPath?: UriComponents | {
        id: string;
    } | {
        light: UriComponents;
        dark: UriComponents;
    };
    options?: {
        status?: {
            description: string;
            kind: ChatResponseReferencePartStatusKind;
        };
    };
}
export interface PromptElementJSON {
    node: PieceJSON;
}
/** Iterates over each {@link PromptNodeJSON} in the tree. */
export declare function forEachNode(node: PromptNodeJSON, fn: (node: PromptNodeJSON) => void): void;
