"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.assertNever = assertNever;
function assertNever(value, msg = `unexpected value ${value}`) {
    throw new Error(`Unreachable: ${msg}`);
}
