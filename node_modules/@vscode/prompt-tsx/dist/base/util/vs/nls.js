"use strict";
//!!! DO NOT modify, this file was COPIED from 'microsoft/vscode'
Object.defineProperty(exports, "__esModule", { value: true });
exports.localize = localize;
exports.localize2 = localize2;
exports.getConfiguredDefaultLocale = getConfiguredDefaultLocale;
function _format(message, args) {
    let result;
    if (args.length === 0) {
        result = message;
    }
    else {
        result = message.replace(/\{(\d+)\}/g, function (match, rest) {
            const index = rest[0];
            return typeof args[index] !== 'undefined' ? args[index] : match;
        });
    }
    return result;
}
function localize(data, message, ...args) {
    return _format(message, args);
}
function localize2(data, message, ...args) {
    const res = _format(message, args);
    return {
        original: res,
        value: res
    };
}
function getConfiguredDefaultLocale(_) {
    return undefined;
}
