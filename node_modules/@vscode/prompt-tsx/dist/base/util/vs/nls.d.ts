export interface ILocalizeInfo {
    key: string;
    comment: string[];
}
interface ILocalizedString {
    original: string;
    value: string;
}
export declare function localize(data: ILocalizeInfo | string, message: string, ...args: any[]): string;
export declare function localize2(data: ILocalizeInfo | string, message: string, ...args: any[]): ILocalizedString;
export declare function getConfiguredDefaultLocale(_: string): undefined;
export {};
