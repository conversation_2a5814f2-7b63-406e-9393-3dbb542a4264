"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.BudgetExceededError = exports.MaterializedChatMessageImage = exports.MaterializedChatMessageBreakpoint = exports.MaterializedChatMessageOpaque = exports.MaterializedChatMessage = exports.MaterializedChatMessageTextChunk = exports.GenericMaterializedContainer = void 0;
const once_1 = require("./once");
const mode_1 = require("./output/mode");
class GenericMaterializedContainer {
    parent;
    id;
    name;
    priority;
    metadata;
    flags;
    children;
    keepWithId;
    constructor(parent, id, name, priority, childrenRef, metadata, flags) {
        this.parent = parent;
        this.id = id;
        this.name = name;
        this.priority = priority;
        this.metadata = metadata;
        this.flags = flags;
        this.children = childrenRef(this);
        if (flags & 8 /* ContainerFlags.EmptyAlternate */) {
            if (this.children.length !== 2) {
                throw new Error('Invalid number of children for EmptyAlternate flag');
            }
            const [ifEmpty, defaultChild] = this.children;
            if (defaultChild.isEmpty) {
                this.children = [ifEmpty];
            }
            else {
                this.children = [defaultChild];
            }
        }
    }
    has(flag) {
        return !!(this.flags & flag);
    }
    /** @inheritdoc */
    async tokenCount(tokenizer) {
        let total = 0;
        await Promise.all(this.children.map(async (child) => {
            const amt = isContainerType(child)
                ? await child.tokenCount(tokenizer)
                : await child.upperBoundTokenCount(tokenizer);
            total += amt;
        }));
        return total;
    }
    /** @inheritdoc */
    async upperBoundTokenCount(tokenizer) {
        let total = 0;
        await Promise.all(this.children.map(async (child) => {
            const amt = await child.upperBoundTokenCount(tokenizer);
            total += amt;
        }));
        return total;
    }
    /**
     * Replaces a node in the tree with the given one, by its ID.
     */
    replaceNode(nodeId, withNode) {
        return replaceNode(nodeId, this.children, withNode);
    }
    /**
     * Gets all metadata the container holds.
     */
    allMetadata() {
        return allMetadata(this);
    }
    /**
     * Finds a node in the tree by ID.
     */
    findById(nodeId) {
        return findNodeById(nodeId, this);
    }
    /**
     * Gets whether the container is empty.
     */
    get isEmpty() {
        return !this.children.some(c => !c.isEmpty);
    }
    /**
     * Called when children change, so caches can be invalidated.
     */
    onChunksChange() {
        this.parent?.onChunksChange();
    }
    /**
     * Gets the chat messages the container holds.
     */
    *toChatMessages() {
        for (const child of this.children) {
            assertContainerOrChatMessage(child);
            if (child instanceof GenericMaterializedContainer) {
                yield* child.toChatMessages();
            }
            else if (!child.isEmpty && child instanceof MaterializedChatMessage) {
                // note: empty messages are already removed during pruning, but the
                // consumer might themselves have given us empty messages that we should omit.
                yield child.toChatMessage();
            }
        }
    }
    async baseMessageTokenCount(tokenizer) {
        let sum = 0;
        await Promise.all(this.children.map(async (child) => {
            if (child instanceof MaterializedChatMessage ||
                child instanceof GenericMaterializedContainer) {
                const amount = await child.baseMessageTokenCount(tokenizer);
                sum += amount;
            }
        }));
        return sum;
    }
    /**
     * Removes the node in the tree with the lowest priority. Returns the
     * list of nodes that were removed.
     */
    removeLowestPriorityChild() {
        const removed = [];
        removeLowestPriorityChild(this, removed);
        return removed;
    }
}
exports.GenericMaterializedContainer = GenericMaterializedContainer;
/** A chunk of text in a {@link MaterializedChatMessage} */
class MaterializedChatMessageTextChunk {
    parent;
    text;
    priority;
    metadata;
    lineBreakBefore;
    constructor(parent, text, priority, metadata = [], lineBreakBefore) {
        this.parent = parent;
        this.text = text;
        this.priority = priority;
        this.metadata = metadata;
        this.lineBreakBefore = lineBreakBefore;
    }
    upperBoundTokenCount(tokenizer) {
        return this._upperBound(tokenizer);
    }
    _upperBound = (0, once_1.once)(async (tokenizer) => {
        const textTokens = await tokenizer.tokenLength({
            type: mode_1.Raw.ChatCompletionContentPartKind.Text,
            text: this.text,
        });
        return textTokens + (this.lineBreakBefore !== 0 /* LineBreakBefore.None */ ? 1 : 0);
    });
    get isEmpty() {
        return !/\S/.test(this.text);
    }
}
exports.MaterializedChatMessageTextChunk = MaterializedChatMessageTextChunk;
class MaterializedChatMessage {
    parent;
    id;
    role;
    name;
    toolCalls;
    toolCallId;
    priority;
    metadata;
    children;
    constructor(parent, id, role, name, toolCalls, toolCallId, priority, metadata, childrenRef) {
        this.parent = parent;
        this.id = id;
        this.role = role;
        this.name = name;
        this.toolCalls = toolCalls;
        this.toolCallId = toolCallId;
        this.priority = priority;
        this.metadata = metadata;
        this.children = childrenRef(this);
    }
    /** @inheritdoc */
    async tokenCount(tokenizer) {
        return this._tokenCount(tokenizer);
    }
    /** @inheritdoc */
    async upperBoundTokenCount(tokenizer) {
        return this._upperBound(tokenizer);
    }
    /** Gets the text this message contains */
    get text() {
        return this._text();
    }
    /** Gets whether the message is empty */
    get isEmpty() {
        return !this.toolCalls?.length && !this.children.some(element => !element.isEmpty);
    }
    /**
     * Replaces a node in the tree with the given one, by its ID.
     */
    replaceNode(nodeId, withNode) {
        const replaced = replaceNode(nodeId, this.children, withNode);
        if (replaced) {
            this.onChunksChange();
        }
        return replaced;
    }
    removeLowestPriorityChild() {
        const removed = [];
        removeLowestPriorityChild(this, removed);
        return removed;
    }
    onChunksChange() {
        this._tokenCount.clear();
        this._upperBound.clear();
        this._text.clear();
        this.parent?.onChunksChange();
    }
    /**
     * Finds a node in the tree by ID.
     */
    findById(nodeId) {
        return findNodeById(nodeId, this);
    }
    _tokenCount = (0, once_1.once)(async (tokenizer) => {
        const raw = this.toChatMessage();
        return tokenizer.countMessageTokens((0, mode_1.toMode)(tokenizer.mode, raw));
    });
    _upperBound = (0, once_1.once)(async (tokenizer) => {
        let total = await this.baseMessageTokenCount(tokenizer);
        await Promise.all(this.children.map(async (chunk) => {
            const amt = await chunk.upperBoundTokenCount(tokenizer);
            total += amt;
        }));
        return total;
    });
    baseMessageTokenCount = (0, once_1.once)((tokenizer) => {
        const raw = this.toChatMessage();
        raw.content = raw.content
            .map(message => {
            if (message.type === mode_1.Raw.ChatCompletionContentPartKind.Text) {
                return { ...message, text: '' };
            }
            else if (message.type === mode_1.Raw.ChatCompletionContentPartKind.Image) {
                return undefined;
            }
            else {
                return message;
            }
        })
            .filter(r => !!r);
        return tokenizer.countMessageTokens((0, mode_1.toMode)(tokenizer.mode, raw));
    });
    _text = (0, once_1.once)(() => {
        let result = [];
        for (const { content, isTextSibling } of contentChunks(this)) {
            if (content instanceof MaterializedChatMessageImage ||
                content instanceof MaterializedChatMessageOpaque) {
                result.push(content);
                continue;
            }
            if (content instanceof MaterializedChatMessageBreakpoint) {
                if (result.at(-1) instanceof MaterializedChatMessageBreakpoint) {
                    result[result.length - 1] = content;
                }
                else {
                    result.push(content);
                }
                continue;
            }
            if (content.lineBreakBefore === 1 /* LineBreakBefore.Always */ ||
                (content.lineBreakBefore === 2 /* LineBreakBefore.IfNotTextSibling */ && !isTextSibling)) {
                let prev = result[result.length - 1];
                if (typeof prev === 'string' && prev && !prev.endsWith('\n')) {
                    result[result.length - 1] = prev + '\n';
                }
            }
            if (typeof result[result.length - 1] === 'string') {
                result[result.length - 1] += content.text;
            }
            else {
                result.push(content.text);
            }
        }
        return result;
    });
    toChatMessage() {
        const content = this.text.map((element) => {
            if (typeof element === 'string') {
                return { type: mode_1.Raw.ChatCompletionContentPartKind.Text, text: element }; // updated type reference
            }
            else if (element instanceof MaterializedChatMessageImage) {
                return {
                    type: mode_1.Raw.ChatCompletionContentPartKind.Image, // updated type reference
                    imageUrl: { url: getEncodedBase64(element.src), detail: element.detail },
                };
            }
            else if (element instanceof MaterializedChatMessageOpaque) {
                return element.value;
            }
            else if (element instanceof MaterializedChatMessageBreakpoint) {
                return element.part;
            }
            else {
                throw new Error('Unexpected element type');
            }
        });
        if (this.role === mode_1.Raw.ChatRole.System) {
            return {
                role: this.role,
                content,
                ...(this.name ? { name: this.name } : {}),
            };
        }
        else if (this.role === mode_1.Raw.ChatRole.Assistant) {
            const msg = { role: this.role, content };
            if (this.name) {
                msg.name = this.name;
            }
            if (this.toolCalls?.length) {
                msg.toolCalls = this.toolCalls.map(tc => ({
                    function: tc.function,
                    id: tc.id,
                    type: tc.type,
                }));
            }
            return msg;
        }
        else if (this.role === mode_1.Raw.ChatRole.User) {
            return {
                role: this.role,
                content,
                ...(this.name ? { name: this.name } : {}),
            };
        }
        else if (this.role === mode_1.Raw.ChatRole.Tool) {
            return {
                role: this.role,
                content,
                toolCallId: this.toolCallId,
            };
        }
        else {
            return {
                role: this.role,
                content,
                name: this.name,
            };
        }
    }
}
exports.MaterializedChatMessage = MaterializedChatMessage;
class MaterializedChatMessageOpaque {
    parent;
    part;
    metadata = [];
    priority = Number.MAX_SAFE_INTEGER;
    get value() {
        return this.part.value;
    }
    constructor(parent, part) {
        this.parent = parent;
        this.part = part;
    }
    upperBoundTokenCount(tokenizer) {
        return this.part.tokenUsage &&
            mode_1.Raw.ChatCompletionContentPartOpaque.usableIn(this.part, tokenizer.mode)
            ? this.part.tokenUsage
            : 0;
    }
    isEmpty = false;
}
exports.MaterializedChatMessageOpaque = MaterializedChatMessageOpaque;
class MaterializedChatMessageBreakpoint {
    parent;
    part;
    metadata = [];
    priority = Number.MAX_SAFE_INTEGER;
    constructor(parent, part) {
        this.parent = parent;
        this.part = part;
    }
    upperBoundTokenCount(_tokenizer) {
        return 0;
    }
    isEmpty = false;
}
exports.MaterializedChatMessageBreakpoint = MaterializedChatMessageBreakpoint;
class MaterializedChatMessageImage {
    parent;
    id;
    src;
    priority;
    metadata;
    lineBreakBefore;
    detail;
    constructor(parent, id, src, priority, metadata = [], lineBreakBefore, detail) {
        this.parent = parent;
        this.id = id;
        this.src = src;
        this.priority = priority;
        this.metadata = metadata;
        this.lineBreakBefore = lineBreakBefore;
        this.detail = detail;
    }
    upperBoundTokenCount(tokenizer) {
        return this._upperBound(tokenizer);
    }
    _upperBound = (0, once_1.once)(async (tokenizer) => {
        return tokenizer.tokenLength({
            type: mode_1.Raw.ChatCompletionContentPartKind.Image,
            imageUrl: { url: getEncodedBase64(this.src), detail: this.detail },
        });
    });
    isEmpty = false;
}
exports.MaterializedChatMessageImage = MaterializedChatMessageImage;
function isContainerType(node) {
    return node instanceof GenericMaterializedContainer || node instanceof MaterializedChatMessage;
}
function isContentType(node) {
    return (node instanceof MaterializedChatMessageTextChunk ||
        node instanceof MaterializedChatMessageImage ||
        node instanceof MaterializedChatMessageOpaque ||
        node instanceof MaterializedChatMessageBreakpoint);
}
function assertContainerOrChatMessage(v) {
    if (!isContainerType(v)) {
        throw new Error(`Cannot have a text node outside a ChatMessage. Text: "${v.text}"`);
    }
}
function* contentChunks(node, isTextSibling = false) {
    for (const child of node.children) {
        if (child instanceof MaterializedChatMessageTextChunk) {
            yield { content: child, isTextSibling };
            isTextSibling = true;
        }
        else if (child instanceof MaterializedChatMessageImage ||
            child instanceof MaterializedChatMessageOpaque ||
            child instanceof MaterializedChatMessageBreakpoint) {
            yield { content: child, isTextSibling: false };
        }
        else if (child instanceof MaterializedChatMessageOpaque) {
            yield { content: child, isTextSibling: true };
        }
        else {
            if (child)
                yield* contentChunks(child, isTextSibling);
            isTextSibling = false;
        }
    }
}
function removeLowestPriorityLegacy(root, removed) {
    let lowest;
    function findLowestInTree(node, chain) {
        if (isContentType(node)) {
            if (!lowest || node.priority < lowest.node.priority) {
                lowest = { chain: chain.slice(), node };
            }
        }
        else {
            chain.push(node);
            for (const child of node.children) {
                findLowestInTree(child, chain);
            }
            chain.pop();
        }
    }
    findLowestInTree(root, []);
    if (!lowest) {
        throw new Error('No lowest priority node found');
    }
    removeNode(lowest.node, removed);
}
// Cache points are never removed, so caching this is safe
const _hasCachePointMemo = new WeakMap();
function hasCachePoint(node) {
    let known = _hasCachePointMemo.get(node);
    if (known !== undefined) {
        return known;
    }
    let result = false;
    if (node instanceof MaterializedChatMessageBreakpoint) {
        result = true;
    }
    else if (node instanceof MaterializedChatMessage) {
        result = node.children.some(c => c instanceof MaterializedChatMessageBreakpoint);
    }
    else if (node instanceof GenericMaterializedContainer) {
        result = node.children.some(hasCachePoint);
    }
    _hasCachePointMemo.set(node, result);
    return result;
}
/**
 * Returns if removeLowestPriorityChild should check for cache breakpoint in
 * the node. This is true only if we aren't nested inside a chat message yet.
 */
function shouldLookForCachePointInNode(node) {
    if (node instanceof MaterializedChatMessage) {
        return true;
    }
    for (let p = node.parent; p; p = p.parent) {
        if (p instanceof MaterializedChatMessage) {
            return false;
        }
    }
    return true;
}
function removeLowestPriorityChild(node, removed) {
    let lowest;
    if (node instanceof GenericMaterializedContainer &&
        node.has(1 /* ContainerFlags.IsLegacyPrioritization */)) {
        removeLowestPriorityLegacy(node, removed);
        return;
    }
    const shouldLookForCachePoint = shouldLookForCachePointInNode(node);
    // In *most* cases the chain is always [node], but it can be longer if
    // the `passPriority` is used. We need to keep track of the chain to
    // call `onChunksChange` as necessary.
    const queue = node.children.map((_, i) => ({ chain: [node], index: i }));
    for (let i = 0; i < queue.length; i++) {
        const { chain, index } = queue[i];
        const child = chain[chain.length - 1].children[index];
        // When a cache point or node containing a cachepoint is encountered, we
        // should reset the 'lowest' node because we will not prune anything before
        // that point.
        if (shouldLookForCachePoint && hasCachePoint(child)) {
            lowest = undefined;
            if (child instanceof MaterializedChatMessageBreakpoint) {
                continue;
            }
        }
        if (child instanceof GenericMaterializedContainer &&
            child.has(4 /* ContainerFlags.PassPriority */) &&
            child.children.length) {
            const newChain = [...chain, child];
            queue.splice(i + 1, 0, ...child.children.map((_, i) => ({ chain: newChain, index: i })));
        }
        else if (!lowest || child.priority < lowest.value.priority) {
            lowest = { chain, index, value: child };
        }
        else if (child.priority === lowest.value.priority) {
            // Use the lowest priority of any of their nested remaining children as a tiebreaker,
            // useful e.g. when dealing with root sibling user vs. system messages
            lowest.lowestNested ??= getLowestPriorityAmongChildren(lowest.value);
            const lowestNestedPriority = getLowestPriorityAmongChildren(child);
            if (lowestNestedPriority < lowest.lowestNested) {
                lowest = { chain, index, value: child, lowestNested: lowestNestedPriority };
            }
        }
    }
    if (!lowest) {
        throw new BudgetExceededError(node);
    }
    if (isContentType(lowest.value) ||
        (lowest.value instanceof GenericMaterializedContainer &&
            lowest.value.has(2 /* ContainerFlags.IsChunk */)) ||
        (isContainerType(lowest.value) && !lowest.value.children.length)) {
        removeNode(lowest.value, removed);
    }
    else {
        removeLowestPriorityChild(lowest.value, removed);
    }
}
/** Thrown when the TSX budget is exceeded and we can't remove elements to reduce it. */
class BudgetExceededError extends Error {
    metadata;
    constructor(node) {
        let path = [node];
        while (path[0].parent) {
            path.unshift(path[0].parent);
        }
        const parts = path.map(n => n instanceof MaterializedChatMessage ? n.role : n.name || '(anonymous)');
        super(`No lowest priority node found (path: ${parts.join(' -> ')})`);
    }
}
exports.BudgetExceededError = BudgetExceededError;
function getLowestPriorityAmongChildren(node) {
    if (!isContainerType(node)) {
        return -1;
    }
    let lowest = Number.MAX_SAFE_INTEGER;
    for (const child of node.children) {
        lowest = Math.min(lowest, child.priority);
    }
    return lowest;
}
function* allMetadata(node) {
    yield* node.metadata;
    for (const child of node.children) {
        if (isContainerType(child)) {
            yield* allMetadata(child);
        }
        else {
            yield* child.metadata;
        }
    }
}
function replaceNode(nodeId, children, withNode) {
    for (let i = 0; i < children.length; i++) {
        const child = children[i];
        if (isContainerType(child)) {
            if (child.id === nodeId) {
                const oldNode = children[i];
                withNode.parent = child.parent;
                children[i] = withNode;
                return oldNode;
            }
            const inner = child.replaceNode(nodeId, withNode);
            if (inner) {
                return inner;
            }
        }
    }
}
function* forEachNode(node) {
    const queue = [node];
    while (queue.length > 0) {
        const current = queue.pop();
        yield current;
        if (isContainerType(current)) {
            queue.push(...current.children);
        }
    }
}
function getRoot(node) {
    let current = node;
    while (current.parent) {
        current = current.parent;
    }
    return current;
}
function isKeepWith(node) {
    return node instanceof GenericMaterializedContainer && node.keepWithId !== undefined;
}
/** Global list of 'keepWiths' currently being removed to avoid recursing indefinitely */
const currentlyBeingRemovedKeepWiths = new Set();
function removeOtherKeepWiths(nodeThatWasRemoved, removed) {
    const removeKeepWithIds = new Set();
    for (const node of forEachNode(nodeThatWasRemoved)) {
        if (isKeepWith(node) && !currentlyBeingRemovedKeepWiths.has(node.keepWithId)) {
            removeKeepWithIds.add(node.keepWithId);
        }
    }
    if (removeKeepWithIds.size === 0) {
        return false;
    }
    for (const id of removeKeepWithIds) {
        currentlyBeingRemovedKeepWiths.add(id);
    }
    try {
        const root = getRoot(nodeThatWasRemoved);
        for (const node of forEachNode(root)) {
            if (isKeepWith(node) && removeKeepWithIds.has(node.keepWithId)) {
                removeNode(node, removed);
            }
            else if (node instanceof MaterializedChatMessage && node.toolCalls) {
                node.toolCalls = filterIfDifferent(node.toolCalls, c => !(c.keepWith && removeKeepWithIds.has(c.keepWith.id)));
                if (node.isEmpty) {
                    // may have become empty if it only contained tool calls
                    removeNode(node, removed);
                }
            }
        }
    }
    finally {
        for (const id of removeKeepWithIds) {
            currentlyBeingRemovedKeepWiths.delete(id);
        }
    }
}
function findNodeById(nodeId, container) {
    if (container.id === nodeId) {
        return container;
    }
    for (const child of container.children) {
        if (isContainerType(child)) {
            const inner = findNodeById(nodeId, child);
            if (inner) {
                return inner;
            }
        }
    }
}
function removeNode(node, removed) {
    const parent = node.parent;
    if (!parent) {
        return; // root
    }
    const index = parent.children.indexOf(node);
    if (index === -1) {
        return;
    }
    parent.children.splice(index, 1);
    removed.push(node);
    removeOtherKeepWiths(node, removed);
    if (parent.isEmpty) {
        removeNode(parent, removed);
    }
    else {
        parent.onChunksChange();
    }
}
function getEncodedBase64(base64String) {
    const mimeTypes = {
        '/9j/': 'image/jpeg',
        iVBOR: 'image/png',
        R0lGOD: 'image/gif',
        UklGR: 'image/webp',
    };
    for (const prefix of Object.keys(mimeTypes)) {
        if (base64String.startsWith(prefix)) {
            return `data:${mimeTypes[prefix]};base64,${base64String}`;
        }
    }
    return base64String;
}
/** Like Array.filter(), but only clones the array if a change is made */
function filterIfDifferent(arr, predicate) {
    for (let i = 0; i < arr.length; i++) {
        if (predicate(arr[i])) {
            continue;
        }
        const newArr = arr.slice(0, i);
        for (let k = i + 1; k < arr.length; k++) {
            if (predicate(arr[k])) {
                newArr.push(arr[k]);
            }
        }
        return newArr;
    }
    return arr;
}
