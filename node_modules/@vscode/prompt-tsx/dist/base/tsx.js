"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
/**
 * Visual Studio Code Prompt Piece
 */
function _vscpp(ctor, props, ...children) {
    return { ctor, props, children: children.flat() };
}
/**
 * Visual Studio Code Prompt Piece Fragment
 */
function _vscppf() {
    throw new Error(`This should not be invoked!`);
}
_vscppf.isFragment = true;
globalThis.vscpp = _vscpp;
globalThis.vscppf = _vscppf;
