import { IElementEpochData, ITraceData, ITraceEpoch, ITracer } from './tracer';
/**
 * Handler that can trace rendering internals into an HTML summary.
 */
export declare class HTMLTracer implements ITracer {
    private traceData?;
    private readonly epochs;
    addRenderEpoch(epoch: ITraceEpoch): void;
    includeInEpoch(data: IElementEpochData): void;
    didMaterializeTree(traceData: ITraceData): void;
    /**
     * Returns HTML to trace the output. Note that is starts a server which is
     * used for client interaction to resize the prompt and its `address` should
     * be displayed or opened as a link in a browser.
     *
     * The server runs until it is disposed.
     */
    serveHTML(): Promise<IHTMLServer>;
    /**
     * Gets an HTML router for a server at the URL. URL is the form `http://127.0.0.1:1234`.
     */
    serveRouter(url: string): IHTMLRouter;
}
export interface IHTMLRouter {
    address: string;
    route(httpIncomingMessage: unknown, httpOutgoingMessage: unknown): boolean;
}
export interface IHTMLServer {
    address: string;
    getHTML(): Promise<string>;
    dispose(): void;
}
