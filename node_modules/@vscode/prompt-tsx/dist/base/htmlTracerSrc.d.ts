export declare const tracerSrc = "\"use strict\";(()=>{var $,m,se,Ue,w,re,le,q,X,G,K,Ae,D={},ce=[],Re=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,J=Array.isArray;function E(t,e){for(var n in e)t[n]=e[n];return t}function ue(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function l(t,e,n){var o,r,_,c={};for(_ in e)_==\"key\"?o=e[_]:_==\"ref\"?r=e[_]:c[_]=e[_];if(arguments.length>2&&(c.children=arguments.length>3?$.call(arguments,2):n),typeof t==\"function\"&&t.defaultProps!=null)for(_ in t.defaultProps)c[_]===void 0&&(c[_]=t.defaultProps[_]);return R(t,c,o,r,null)}function R(t,e,n,o,r){var _={type:t,props:e,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:r??++se,__i:-1,__u:0};return r==null&&m.vnode!=null&&m.vnode(_),_}function N(t){return t.children}function B(t,e){this.props=t,this.context=e}function I(t,e){if(e==null)return t.__?I(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null)return n.__e;return typeof t.type==\"function\"?I(t):null}function de(t){var e,n;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null){t.__e=t.__c.base=n.__e;break}return de(t)}}function ie(t){(!t.__d&&(t.__d=!0)&&w.push(t)&&!O.__r++||re!==m.debounceRendering)&&((re=m.debounceRendering)||le)(O)}function O(){var t,e,n,o,r,_,c,a;for(w.sort(q);t=w.shift();)t.__d&&(e=w.length,o=void 0,_=(r=(n=t).__v).__e,c=[],a=[],n.__P&&((o=E({},r)).__v=r.__v+1,m.vnode&&m.vnode(o),Q(n.__P,o,r,n.__n,n.__P.namespaceURI,32&r.__u?[_]:null,c,_??I(r),!!(32&r.__u),a),o.__v=r.__v,o.__.__k[o.__i]=o,me(c,o,a),o.__e!=_&&de(o)),w.length>e&&w.sort(q));O.__r=0}function pe(t,e,n,o,r,_,c,a,u,s,p){var i,f,d,b,x,C=o&&o.__k||ce,h=e.length;for(n.__d=u,Be(n,e,C),u=n.__d,i=0;i<h;i++)(d=n.__k[i])!=null&&(f=d.__i===-1?D:C[d.__i]||D,d.__i=i,Q(t,d,f,r,_,c,a,u,s,p),b=d.__e,d.ref&&f.ref!=d.ref&&(f.ref&&Y(f.ref,null,d),p.push(d.ref,d.__c||b,d)),x==null&&b!=null&&(x=b),65536&d.__u||f.__k===d.__k?u=fe(d,u,t):typeof d.type==\"function\"&&d.__d!==void 0?u=d.__d:b&&(u=b.nextSibling),d.__d=void 0,d.__u&=-196609);n.__d=u,n.__e=x}function Be(t,e,n){var o,r,_,c,a,u=e.length,s=n.length,p=s,i=0;for(t.__k=[],o=0;o<u;o++)(r=e[o])!=null&&typeof r!=\"boolean\"&&typeof r!=\"function\"?(c=o+i,(r=t.__k[o]=typeof r==\"string\"||typeof r==\"number\"||typeof r==\"bigint\"||r.constructor==String?R(null,r,null,null,null):J(r)?R(N,{children:r},null,null,null):r.constructor===void 0&&r.__b>0?R(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r).__=t,r.__b=t.__b+1,_=null,(a=r.__i=Oe(r,n,c,p))!==-1&&(p--,(_=n[a])&&(_.__u|=131072)),_==null||_.__v===null?(a==-1&&i--,typeof r.type!=\"function\"&&(r.__u|=65536)):a!==c&&(a==c-1?i--:a==c+1?i++:(a>c?i--:i++,r.__u|=65536))):r=t.__k[o]=null;if(p)for(o=0;o<s;o++)(_=n[o])!=null&&!(131072&_.__u)&&(_.__e==t.__d&&(t.__d=I(_)),he(_,_))}function fe(t,e,n){var o,r;if(typeof t.type==\"function\"){for(o=t.__k,r=0;o&&r<o.length;r++)o[r]&&(o[r].__=t,e=fe(o[r],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=I(t)),n.insertBefore(t.__e,e||null),e=t.__e);do e=e&&e.nextSibling;while(e!=null&&e.nodeType===8);return e}function Oe(t,e,n,o){var r=t.key,_=t.type,c=n-1,a=n+1,u=e[n];if(u===null||u&&r==u.key&&_===u.type&&!(131072&u.__u))return n;if(o>(u!=null&&!(131072&u.__u)?1:0))for(;c>=0||a<e.length;){if(c>=0){if((u=e[c])&&!(131072&u.__u)&&r==u.key&&_===u.type)return c;c--}if(a<e.length){if((u=e[a])&&!(131072&u.__u)&&r==u.key&&_===u.type)return a;a++}}return-1}function _e(t,e,n){e[0]===\"-\"?t.setProperty(e,n??\"\"):t[e]=n==null?\"\":typeof n!=\"number\"||Re.test(e)?n:n+\"px\"}function A(t,e,n,o,r){var _;e:if(e===\"style\")if(typeof n==\"string\")t.style.cssText=n;else{if(typeof o==\"string\"&&(t.style.cssText=o=\"\"),o)for(e in o)n&&e in n||_e(t.style,e,\"\");if(n)for(e in n)o&&n[e]===o[e]||_e(t.style,e,n[e])}else if(e[0]===\"o\"&&e[1]===\"n\")_=e!==(e=e.replace(/(PointerCapture)$|Capture$/i,\"$1\")),e=e.toLowerCase()in t||e===\"onFocusOut\"||e===\"onFocusIn\"?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+_]=n,n?o?n.u=o.u:(n.u=X,t.addEventListener(e,_?K:G,_)):t.removeEventListener(e,_?K:G,_);else{if(r==\"http://www.w3.org/2000/svg\")e=e.replace(/xlink(H|:h)/,\"h\").replace(/sName$/,\"s\");else if(e!=\"width\"&&e!=\"height\"&&e!=\"href\"&&e!=\"list\"&&e!=\"form\"&&e!=\"tabIndex\"&&e!=\"download\"&&e!=\"rowSpan\"&&e!=\"colSpan\"&&e!=\"role\"&&e!=\"popover\"&&e in t)try{t[e]=n??\"\";break e}catch{}typeof n==\"function\"||(n==null||n===!1&&e[4]!==\"-\"?t.removeAttribute(e):t.setAttribute(e,e==\"popover\"&&n==1?\"\":n))}}function ae(t){return function(e){if(this.l){var n=this.l[e.type+t];if(e.t==null)e.t=X++;else if(e.t<n.u)return;return n(m.event?m.event(e):e)}}}function Q(t,e,n,o,r,_,c,a,u,s){var p,i,f,d,b,x,C,h,v,H,M,P,F,oe,z,j,k=e.type;if(e.constructor!==void 0)return null;128&n.__u&&(u=!!(32&n.__u),_=[a=e.__e=n.__e]),(p=m.__b)&&p(e);e:if(typeof k==\"function\")try{if(h=e.props,v=\"prototype\"in k&&k.prototype.render,H=(p=k.contextType)&&o[p.__c],M=p?H?H.props.value:p.__:o,n.__c?C=(i=e.__c=n.__c).__=i.__E:(v?e.__c=i=new k(h,M):(e.__c=i=new B(h,M),i.constructor=k,i.render=We),H&&H.sub(i),i.props=h,i.state||(i.state={}),i.context=M,i.__n=o,f=i.__d=!0,i.__h=[],i._sb=[]),v&&i.__s==null&&(i.__s=i.state),v&&k.getDerivedStateFromProps!=null&&(i.__s==i.state&&(i.__s=E({},i.__s)),E(i.__s,k.getDerivedStateFromProps(h,i.__s))),d=i.props,b=i.state,i.__v=e,f)v&&k.getDerivedStateFromProps==null&&i.componentWillMount!=null&&i.componentWillMount(),v&&i.componentDidMount!=null&&i.__h.push(i.componentDidMount);else{if(v&&k.getDerivedStateFromProps==null&&h!==d&&i.componentWillReceiveProps!=null&&i.componentWillReceiveProps(h,M),!i.__e&&(i.shouldComponentUpdate!=null&&i.shouldComponentUpdate(h,i.__s,M)===!1||e.__v===n.__v)){for(e.__v!==n.__v&&(i.props=h,i.state=i.__s,i.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some(function(U){U&&(U.__=e)}),P=0;P<i._sb.length;P++)i.__h.push(i._sb[P]);i._sb=[],i.__h.length&&c.push(i);break e}i.componentWillUpdate!=null&&i.componentWillUpdate(h,i.__s,M),v&&i.componentDidUpdate!=null&&i.__h.push(function(){i.componentDidUpdate(d,b,x)})}if(i.context=M,i.props=h,i.__P=t,i.__e=!1,F=m.__r,oe=0,v){for(i.state=i.__s,i.__d=!1,F&&F(e),p=i.render(i.props,i.state,i.context),z=0;z<i._sb.length;z++)i.__h.push(i._sb[z]);i._sb=[]}else do i.__d=!1,F&&F(e),p=i.render(i.props,i.state,i.context),i.state=i.__s;while(i.__d&&++oe<25);i.state=i.__s,i.getChildContext!=null&&(o=E(E({},o),i.getChildContext())),v&&!f&&i.getSnapshotBeforeUpdate!=null&&(x=i.getSnapshotBeforeUpdate(d,b)),pe(t,J(j=p!=null&&p.type===N&&p.key==null?p.props.children:p)?j:[j],e,n,o,r,_,c,a,u,s),i.base=e.__e,e.__u&=-161,i.__h.length&&c.push(i),C&&(i.__E=i.__=null)}catch(U){if(e.__v=null,u||_!=null){for(e.__u|=u?160:32;a&&a.nodeType===8&&a.nextSibling;)a=a.nextSibling;_[_.indexOf(a)]=null,e.__e=a}else e.__e=n.__e,e.__k=n.__k;m.__e(U,e,n)}else _==null&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):e.__e=$e(n.__e,e,n,o,r,_,c,u,s);(p=m.diffed)&&p(e)}function me(t,e,n){e.__d=void 0;for(var o=0;o<n.length;o++)Y(n[o],n[++o],n[++o]);m.__c&&m.__c(e,t),t.some(function(r){try{t=r.__h,r.__h=[],t.some(function(_){_.call(r)})}catch(_){m.__e(_,r.__v)}})}function $e(t,e,n,o,r,_,c,a,u){var s,p,i,f,d,b,x,C=n.props,h=e.props,v=e.type;if(v===\"svg\"?r=\"http://www.w3.org/2000/svg\":v===\"math\"?r=\"http://www.w3.org/1998/Math/MathML\":r||(r=\"http://www.w3.org/1999/xhtml\"),_!=null){for(s=0;s<_.length;s++)if((d=_[s])&&\"setAttribute\"in d==!!v&&(v?d.localName===v:d.nodeType===3)){t=d,_[s]=null;break}}if(t==null){if(v===null)return document.createTextNode(h);t=document.createElementNS(r,v,h.is&&h),a&&(m.__m&&m.__m(e,_),a=!1),_=null}if(v===null)C===h||a&&t.data===h||(t.data=h);else{if(_=_&&$.call(t.childNodes),C=n.props||D,!a&&_!=null)for(C={},s=0;s<t.attributes.length;s++)C[(d=t.attributes[s]).name]=d.value;for(s in C)if(d=C[s],s!=\"children\"){if(s==\"dangerouslySetInnerHTML\")i=d;else if(!(s in h)){if(s==\"value\"&&\"defaultValue\"in h||s==\"checked\"&&\"defaultChecked\"in h)continue;A(t,s,null,d,r)}}for(s in h)d=h[s],s==\"children\"?f=d:s==\"dangerouslySetInnerHTML\"?p=d:s==\"value\"?b=d:s==\"checked\"?x=d:a&&typeof d!=\"function\"||C[s]===d||A(t,s,d,C[s],r);if(p)a||i&&(p.__html===i.__html||p.__html===t.innerHTML)||(t.innerHTML=p.__html),e.__k=[];else if(i&&(t.innerHTML=\"\"),pe(t,J(f)?f:[f],e,n,o,v===\"foreignObject\"?\"http://www.w3.org/1999/xhtml\":r,_,c,_?_[0]:n.__k&&I(n,0),a,u),_!=null)for(s=_.length;s--;)ue(_[s]);a||(s=\"value\",v===\"progress\"&&b==null?t.removeAttribute(\"value\"):b!==void 0&&(b!==t[s]||v===\"progress\"&&!b||v===\"option\"&&b!==C[s])&&A(t,s,b,C[s],r),s=\"checked\",x!==void 0&&x!==t[s]&&A(t,s,x,C[s],r))}return t}function Y(t,e,n){try{if(typeof t==\"function\"){var o=typeof t.__u==\"function\";o&&t.__u(),o&&e==null||(t.__u=t(e))}else t.current=e}catch(r){m.__e(r,n)}}function he(t,e,n){var o,r;if(m.unmount&&m.unmount(t),(o=t.ref)&&(o.current&&o.current!==t.__e||Y(o,null,e)),(o=t.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(_){m.__e(_,e)}o.base=o.__P=null}if(o=t.__k)for(r=0;r<o.length;r++)o[r]&&he(o[r],e,n||typeof t.type!=\"function\");n||ue(t.__e),t.__c=t.__=t.__e=t.__d=void 0}function We(t,e,n){return this.constructor(t,n)}function ve(t,e,n){var o,r,_,c;m.__&&m.__(t,e),r=(o=typeof n==\"function\")?null:n&&n.__k||e.__k,_=[],c=[],Q(e,t=(!o&&n||e).__k=l(N,null,[t]),r||D,D,e.namespaceURI,!o&&n?[n]:r?null:e.firstChild?$.call(e.childNodes):null,_,!o&&n?n:r?r.__e:e.firstChild,o,c),me(_,t,c)}$=ce.slice,m={__e:function(t,e,n,o){for(var r,_,c;e=e.__;)if((r=e.__c)&&!r.__)try{if((_=r.constructor)&&_.getDerivedStateFromError!=null&&(r.setState(_.getDerivedStateFromError(t)),c=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(t,o||{}),c=r.__d),c)return r.__E=r}catch(a){t=a}throw t}},se=0,Ue=function(t){return t!=null&&t.constructor==null},B.prototype.setState=function(t,e){var n;n=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=E({},this.state),typeof t==\"function\"&&(t=t(E({},n),this.props)),t&&E(n,t),t!=null&&this.__v&&(e&&this._sb.push(e),ie(this))},B.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),ie(this))},B.prototype.render=N,w=[],le=typeof Promise==\"function\"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,q=function(t,e){return t.__v.__b-e.__v.__b},O.__r=0,X=0,G=ae(!1),K=ae(!0),Ae=0;var L,g,Z,ge,V=0,Ee=[],y=m,be=y.__b,ye=y.__r,Ce=y.diffed,xe=y.__c,ke=y.unmount,Te=y.__;function te(t,e){y.__h&&y.__h(g,t,V||e),V=0;var n=g.__H||(g.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function S(t){return V=1,Ve(Ne,t)}function Ve(t,e,n){var o=te(L++,2);if(o.t=t,!o.__c&&(o.__=[n?n(e):Ne(void 0,e),function(a){var u=o.__N?o.__N[0]:o.__[0],s=o.t(u,a);u!==s&&(o.__N=[s,o.__[1]],o.__c.setState({}))}],o.__c=g,!g.u)){var r=function(a,u,s){if(!o.__c.__H)return!0;var p=o.__c.__H.__.filter(function(f){return!!f.__c});if(p.every(function(f){return!f.__N}))return!_||_.call(this,a,u,s);var i=!1;return p.forEach(function(f){if(f.__N){var d=f.__[0];f.__=f.__N,f.__N=void 0,d!==f.__[0]&&(i=!0)}}),!(!i&&o.__c.props===a)&&(!_||_.call(this,a,u,s))};g.u=!0;var _=g.shouldComponentUpdate,c=g.componentWillUpdate;g.componentWillUpdate=function(a,u,s){if(this.__e){var p=_;_=void 0,r(a,u,s),_=p}c&&c.call(this,a,u,s)},g.shouldComponentUpdate=r}return o.__N||o.__}function Se(t,e){var n=te(L++,3);!y.__s&&Ie(n.__H,e)&&(n.__=t,n.i=e,g.__H.__h.push(n))}function we(t){return V=5,je(function(){return{current:t}},[])}function je(t,e){var n=te(L++,7);return Ie(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function qe(){for(var t;t=Ee.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(W),t.__H.__h.forEach(ee),t.__H.__h=[]}catch(e){t.__H.__h=[],y.__e(e,t.__v)}}y.__b=function(t){g=null,be&&be(t)},y.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),Te&&Te(t,e)},y.__r=function(t){ye&&ye(t),L=0;var e=(g=t.__c).__H;e&&(Z===g?(e.__h=[],g.__h=[],e.__.forEach(function(n){n.__N&&(n.__=n.__N),n.i=n.__N=void 0})):(e.__h.forEach(W),e.__h.forEach(ee),e.__h=[],L=0)),Z=g},y.diffed=function(t){Ce&&Ce(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(Ee.push(e)!==1&&ge===y.requestAnimationFrame||((ge=y.requestAnimationFrame)||Ge)(qe)),e.__H.__.forEach(function(n){n.i&&(n.__H=n.i),n.i=void 0})),Z=g=null},y.__c=function(t,e){e.some(function(n){try{n.__h.forEach(W),n.__h=n.__h.filter(function(o){return!o.__||ee(o)})}catch(o){e.some(function(r){r.__h&&(r.__h=[])}),e=[],y.__e(o,n.__v)}}),xe&&xe(t,e)},y.unmount=function(t){ke&&ke(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach(function(o){try{W(o)}catch(r){e=r}}),n.__H=void 0,e&&y.__e(e,n.__v))};var Me=typeof requestAnimationFrame==\"function\";function Ge(t){var e,n=function(){clearTimeout(o),Me&&cancelAnimationFrame(e),setTimeout(t)},o=setTimeout(n,100);Me&&(e=requestAnimationFrame(n))}function W(t){var e=g,n=t.__c;typeof n==\"function\"&&(t.__c=void 0,n()),g=e}function ee(t){var e=g;t.__c=t.__(),g=e}function Ie(t,e){return!t||t.length!==e.length||e.some(function(n,o){return n!==t[o]})}function Ne(t,e){return typeof e==\"function\"?e(t):e}function He(t,e){let n=we(void 0),o=(...r)=>{n.current&&clearTimeout(n.current),n.current=window.setTimeout(()=>{t(...r)},e)};return Se(()=>()=>{n.current&&clearTimeout(n.current)},[]),o}var Ke=new Intl.NumberFormat(\"en-US\"),T=({value:t})=>l(N,null,Ke.format(t));var ne=[{bg:\"#c1e7ff\",fg:\"#000\"},{bg:\"#abd2ec\",fg:\"#000\"},{bg:\"#94bed9\",fg:\"#000\"},{bg:\"#7faac6\",fg:\"#000\"},{bg:\"#6996b3\",fg:\"#fff\"},{bg:\"#5383a1\",fg:\"#fff\"},{bg:\"#3d708f\",fg:\"#fff\"},{bg:\"#255e7e\",fg:\"#fff\"}],Xe=({scoreBy:t,nodes:e,epoch:n})=>{if(e.length===0)return null;let o=t;if(t.field!==\"tokens\"){let r=e[0][t.field],_=e[0][t.field];for(let c=1;c<e.length;c++)r=Math.max(r,e[c][t.field]),_=Math.max(_,e[c][t.field]);o={field:t.field,max:r,min:_}}return l(\"div\",{className:\"node-children\"},e.map((r,_)=>r.type===2?l(Je,{scoreBy:o,key:_,node:r}):l(Le,{scoreBy:o,key:_,node:r,epoch:n})))},Fe=({node:t})=>l(\"div\",{className:\"node-stats\"},\"Used Tokens: \",l(T,{value:t.tokens}),\" / \",\"Priority:\",\" \",t.priority===Number.MAX_SAFE_INTEGER?\"MAX\":l(T,{value:t.priority})),De=({scoreBy:t,node:e,children:n,...o})=>{let r=0;if(t.max!==t.min){let _=(e[t.field]-t.min)/(t.max-t.min);r=Math.round((ne.length-1)*_)}return l(\"div\",{...o,className:`node ${o.className||\"\"}`,style:{backgroundColor:ne[r].bg,color:ne[r].fg}},n)},Je=({scoreBy:t,node:e})=>l(De,{node:e,scoreBy:t,tabIndex:0,className:\"node-text\"},l(Fe,{node:e}),l(\"div\",{className:\"node-content\"},e.value)),Le=({scoreBy:t,node:e,epoch:n})=>{let[o,r]=S(!1),_=EPOCHS.findIndex(i=>i.elements.some(f=>f.id===e.id));if(_===void 0)throw new Error(`epoch not found for ${e.id}`);let c=EPOCHS[_],a=EPOCHS.at(n),u=c.elements.find(i=>i.id===e.id).tokenBudget,s=e.type===1?e.name||e.role.slice(0,1).toUpperCase()+e.role.slice(1)+\"Message\":e.name,p=_===n?\"new-in-epoch\":n<_?\"before-epoch\":\"\";return l(De,{node:e,scoreBy:t,className:p},l(Fe,{node:e}),l(\"div\",{className:\"node-content node-toggler\",onClick:()=>r(i=>!i)},l(\"span\",null,a?.inNode===e.id?\"\\u{1F3C3} \":\"\",`<${s}>`),l(\"span\",{className:\"indicator\"},o?\"[+]\":\"[-]\")),n===_&&l(\"div\",{className:\"node-stats\"},\"Token Budget: \",l(T,{value:u})),a?.inNode===e.id&&l(\"div\",{className:\"node-stats\"},\"Rendering flexGrow=\",a.flexValue,l(\"br\",null),l(\"br\",null),\"Splitting\",\" \",a.reservedTokens?`${a.tokenBudget} - ${a.reservedTokens} (reserved) = `:\"\",l(T,{value:a.tokenBudget}),\" tokens among \",a.elements.length,\" \",\"elements\"),!o&&l(Xe,{nodes:e.children,scoreBy:t,epoch:n}))},Pe=({scoreBy:t,node:e,epoch:n})=>{let o;return t===\"tokens\"?o={field:\"tokens\",max:e.tokens,min:0}:o={field:\"priority\",max:e.priority,min:e.priority},l(Le,{scoreBy:o,node:e,epoch:n})};var ze=({label:t,value:e,onChange:n,min:o,max:r})=>{let _=a=>{n(a.target.valueAsNumber)},c=`number-slider-${Math.random()}`;return l(\"div\",{className:\"controls-slider\"},l(\"label\",{htmlFor:c},t),l(\"input\",{id:c,type:\"range\",min:o,max:r,value:e,onInput:_}),l(\"input\",{type:\"number\",min:o,value:e,onInput:_,onChange:_}))},Qe=({scoreBy:t,onScoreByChange:e})=>{let n=o=>{let r=o.target.value;e(r)};return l(\"div\",{className:\"controls-scoreby\"},\"Visualize by\",l(\"label\",null,l(\"input\",{type:\"radio\",name:\"scoreBy\",value:\"tokens\",checked:t===\"tokens\",onChange:n}),\"Tokens\"),l(\"label\",null,l(\"input\",{type:\"radio\",name:\"scoreBy\",value:\"priority\",checked:t===\"priority\",onChange:n}),\"Priority\"))},Ye=()=>{let[t,e]=S(DEFAULT_TOKENS),[n,o]=S(EPOCHS.length),[r,_]=S(DEFAULT_MODEL),[c,a]=S(\"tokens\"),[u,s]=S(\"epoch\"),p=He(async f=>{if(f===DEFAULT_TOKENS)return DEFAULT_MODEL;let b=await(await fetch(`${SERVER_ADDRESS}regen?n=${f}`)).json();_(b)},100),i=f=>{e(f),p(f),o(EPOCHS.length)};return l(\"div\",{className:\"app\"},l(\"div\",{className:\"controls\"},l(\"div\",{className:\"tabs\"},l(\"div\",{className:`tab ${u===\"epoch\"?\"active\":\"\"}`,onClick:()=>s(\"epoch\")},\"View Order\"),l(\"div\",{className:`tab ${u===\"tokens\"?\"active\":\"\"}`,onClick:()=>s(\"tokens\")},\"Change Token Budget\")),l(\"div\",{className:`tab-content ${u===\"epoch\"?\"active\":\"\"}`},l(ze,{label:\"Render Epoch\",value:n,onChange:o,min:0,max:EPOCHS.length})),l(\"div\",{className:`tab-content ${u===\"tokens\"?\"active\":\"\"}`},l(ze,{label:\"Token Budget\",value:t,onChange:i,min:0,max:DEFAULT_TOKENS*2}))),l(\"div\",{className:\"control-description\"},u===\"tokens\"?l(\"p\",null,\"Token changes here will prune elements and re-render Expandable ones, but the entire prompt is not being re-rendered\"):l(\"p\",null,\"Changing the render epoch lets you see the order in which elements are rendered and how the token budget is allocated.\"),l(\"div\",{className:\"controls-stats\"},l(\"span\",null,\"Used \",l(T,{value:r.container.tokens}),\"/\",l(T,{value:r.budget}),\" tokens\"),l(\"span\",null,\"Removed \",l(T,{value:r.removed}),\" nodes\"),l(Qe,{scoreBy:c,onScoreByChange:a}))),l(Pe,{node:r.container,scoreBy:c,epoch:n}))};ve(l(Ye,null),document.body);})();\n";
export declare const tracerCss = "body{font-family:-apple-system,BlinkMacSystemFont,Segoe WPC,Segoe UI,system-ui,Ubuntu,Droid Sans,sans-serif;background:#fff;margin:0}.render-pass{border-left:2px solid #ccc;&:hover{border-left-color:#000}}.literals li{white-space:pre;font-family:SF Mono,Monaco,Menlo,Consolas,Ubuntu Mono,Liberation Mono,DejaVu Sans Mono,Courier New,monospace}.render-flex,.render-element{padding-left:10px}.node{border:1px solid rgba(255,255,255,.5);margin:3px 10px;padding:3px 10px;border-radius:4px;width:fit-content;&.new-in-epoch{box-shadow:0 0 3px 2px red}&.before-epoch{pointer-events:none;filter:grayscale(1);color:#777!important;.node{color:#777!important}}&:last-child{margin-bottom:0}}.node-content{font-weight:700}.node-children{margin-left:20px;border-left:2px dashed rgba(255,255,255,.5);padding-left:10px}.node-toggler{cursor:pointer;display:flex;align-items:center;justify-content:space-between;.indicator{font-size:.7em}}.node-text{width:400px;&:focus,&:focus-within{outline:1px solid orange;.node-content{white-space:normal}}.node-content{font-weight:400;font-size:.8em;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}}.node-stats{font-family:SF Mono,Monaco,Menlo,Consolas,Ubuntu Mono,Liberation Mono,DejaVu Sans Mono,Courier New,monospace;font-size:.8em}.control-description{padding:10px;p{font-size:.9em;max-width:500px;margin-top:0}}.controls{display:flex;flex-direction:column;gap:10px;position:sticky;top:0;padding:10px;background:#fff;border-bottom:1px solid #ccc;z-index:1}.controls-slider{display:flex;align-items:center;gap:10px}.controls-stats{display:flex;gap:20px;list-style:none;padding:0;margin-top:0}.controls-scoreby{display:flex;gap:10px}.tabs{display:flex;border-bottom:1px solid #ccc;margin-bottom:10px}.tab{padding:10px;cursor:pointer;border:1px solid transparent;border-bottom:none}.tab.active{border-color:#ccc;border-bottom:1px solid #fff;background-color:#f9f9f9}.tab-content{display:none}.tab-content.active{display:block}\n";
