"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeTokenizer = void 0;
const mode_1 = require("../output/mode");
class VSCodeTokenizer {
    countTokens;
    mode = mode_1.OutputMode.VSCode;
    constructor(countTokens, mode) {
        this.countTokens = countTokens;
        if (mode !== mode_1.OutputMode.VSCode) {
            throw new Error('`mode` must be set to vscode when using vscode.LanguageModelChat as the tokenizer');
        }
    }
    async tokenLength(part, token) {
        if (part.type === mode_1.Raw.ChatCompletionContentPartKind.Text) {
            return this.countTokens(part.text, token);
        }
        return Promise.resolve(0);
    }
    async countMessageTokens(message) {
        return this.countTokens(message);
    }
}
exports.VSCodeTokenizer = VSCodeTokenizer;
