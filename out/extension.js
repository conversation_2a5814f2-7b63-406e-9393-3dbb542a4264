"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const github_1 = require("./api/github");
const commands_1 = require("./commands");
const contextManager_1 = require("./services/contextManager");
const download_1 = require("./services/download"); // Import IDownloadService
const tools_1 = require("./tools/tools");
const explorerStateService_1 = require("./services/explorerStateService");
const httpClient_1 = require("./services/httpClient");
const logger_1 = require("./services/logger");
const rateLimitManager_1 = require("./services/rateLimitManager");
const selection_1 = require("./services/selection");
const statusBarService_1 = require("./services/statusBarService");
const storage_1 = require("./services/storage");
const updateCheckService_1 = require("./services/updateCheckService");
const explorerView_1 = require("./views/explorer/explorerView");
// Removed import for UpdatesTreeProvider
const welcomeView_1 = require("./views/welcome/welcomeView");
function activate(context) {
    const logger = new logger_1.Logger("AI-Driven Dev Rules", true);
    logger.info("AI-Driven Dev Rules extension is now active");
    const storageService = new storage_1.StorageService(context);
    const config = vscode.workspace.getConfiguration("aidd");
    const showWelcomeOnStartup = config.get("showWelcomeOnStartup") ?? true;
    const autoRefreshInterval = config.get("autoRefreshInterval", null);
    const httpClient = new httpClient_1.HttpClient(logger);
    const rateLimitManager = new rateLimitManager_1.RateLimitManager(logger);
    const explorerStateService = new explorerStateService_1.ExplorerStateService(logger);
    const githubService = new github_1.GitHubApiService(httpClient, rateLimitManager, logger);
    // Instantiate DownloadService with context
    const downloadService = new download_1.DownloadService(logger, httpClient, githubService, context // Pass context
    );
    const selectionService = new selection_1.SelectionService(logger, explorerStateService);
    // Instantiate UpdateCheckService
    const updateCheckService = new updateCheckService_1.UpdateCheckService(logger, githubService, context);
    // Instantiate StatusBarService
    const statusBarService = new statusBarService_1.StatusBarService(logger);
    context.subscriptions.push(statusBarService);
    // Instantiate ContextManager for xendit-copilot functionality
    const contextManager = new contextManager_1.ContextManager(logger, githubService, storageService);
    // Removed instantiation of UpdatesTreeProvider
    const explorerView = new explorerView_1.ExplorerView(context, githubService, logger, storageService, downloadService, selectionService, explorerStateService, updateCheckService, statusBarService); // Correctly removed updatesTreeProvider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(welcomeView_1.WelcomeView.VIEW_ID, {
        resolveWebviewView(webviewView) {
            new welcomeView_1.WelcomeView(webviewView, storageService, logger);
        },
    }));
    (0, commands_1.registerCommands)({
        context,
        explorerView,
        githubService,
        logger,
        storageService,
        // updatesTreeProvider is no longer needed here
    });
    // Register xendit-copilot chat tools
    (0, tools_1.registerChatTools)(context, logger, contextManager);
    // Initial setup: Link repository changes from ExplorerView to UpdatesTreeProvider
    // This requires ExplorerView to expose an event or have a method called by registerCommands
    // Let's modify registerCommands to handle this linkage.
    if (showWelcomeOnStartup) {
        vscode.commands.executeCommand("aidd.welcomeView.focus");
    }
    setupAutoRefresh(context, autoRefreshInterval, logger);
    // Listen for configuration changes to refresh the view if includePaths changes
    context.subscriptions.push(vscode.workspace.onDidChangeConfiguration((event) => {
        if (event.affectsConfiguration("aidd.includePaths")) {
            logger.info("Configuration 'aidd.includePaths' changed. Refreshing explorer view.");
            // Refresh the main explorer view to apply the new filter
            explorerView.refreshView();
            // Optionally, refresh the updates view too, although filtering isn't applied there
            // updatesTreeProvider.refresh();
        }
    }));
    // define a chat handler
    const handler = async (request, context, stream, token) => {
        // initialize the prompt
        const BASE_PROMPT = "You are a helpful code tutor. Your job is to teach the user with simple descriptions and sample code of the concept. Respond with a guided overview of the concept in a series of messages. Do not give the user the answer directly, but guide them to find the answer themselves. If the user asks a non-programming question, politely decline to respond.";
        let prompt = BASE_PROMPT;
        // initialize the messages array with the prompt
        const messages = [vscode.LanguageModelChatMessage.User(prompt)];
        // Inject repository context
        const lastRepo = storageService.getLastRepository();
        if (lastRepo) {
            messages.push(vscode.LanguageModelChatMessage.User(`Current repository: ${lastRepo.owner}/${lastRepo.name}`));
        }
        // add in the user's message
        messages.push(vscode.LanguageModelChatMessage.User(request.prompt));
        // send the request
        const chatResponse = await request.model.sendRequest(messages, {}, token);
        // stream the response
        for await (const fragment of chatResponse.text) {
            stream.markdown(fragment);
        }
        return;
    };
    const tutor = vscode.chat.createChatParticipant("chat-tutorial.code-tutor", handler);
}
function setupAutoRefresh(context, autoRefreshInterval, logger) {
    if (typeof autoRefreshInterval !== "number" || autoRefreshInterval < 10) {
        return;
    }
    const intervalMs = autoRefreshInterval * 1000;
    const interval = setInterval(() => {
        logger.debug(`Auto-refreshing repository (interval: ${autoRefreshInterval}s)`);
        vscode.commands.executeCommand("aidd.refresh");
    }, intervalMs);
    context.subscriptions.push({
        dispose: () => clearInterval(interval),
    });
}
function deactivate() { }
//# sourceMappingURL=extension.js.map